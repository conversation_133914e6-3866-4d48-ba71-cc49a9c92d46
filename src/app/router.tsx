import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Spinner } from '@shared/ui';
import { Layout } from './Layout';
import { 
  HomePage, 
  ProductsPage, 
  AboutPage, 
  ContactPage, 
  ShopPage 
} from '@pages';

// Loading fallback component
const PageLoader: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center">
      <Spinner size="lg" />
      <p className="mt-4 text-slate-600 dark:text-slate-400">
        Cargando página...
      </p>
    </div>
  </div>
);

export const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <Layout>
        <Suspense fallback={<PageLoader />}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/productos" element={<ProductsPage />} />
            <Route path="/nosotros" element={<AboutPage />} />
            <Route path="/contacto" element={<ContactPage />} />
            <Route path="/tienda" element={<ShopPage />} />
            
            {/* Catch-all route for 404 */}
            <Route path="*" element={
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
                    404
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400">
                    Página no encontrada
                  </p>
                </div>
              </div>
            } />
          </Routes>
        </Suspense>
      </Layout>
    </BrowserRouter>
  );
};
