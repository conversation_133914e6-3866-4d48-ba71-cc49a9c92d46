import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Mail, Phone, MapPin } from 'lucide-react';
import { FaFacebook, FaInstagram, FaLinkedin, FaWhatsapp } from 'react-icons/fa';
import { COMPANY_INFO } from '@shared/constants';

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <img
                src="/logo/hidroimplementos-logo-white.png"
                alt="Hidroimplementos SPA"
                className="h-10 w-auto"
              />
              <span className="font-bold text-xl">Hidroimplementos SPA</span>
            </div>
            <p className="text-slate-300 mb-6 max-w-md">
              Soluciones integrales en sistemas de riego y bombeo. 
              Más de 15 años de experiencia al servicio de la agricultura chilena.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Phone size={16} className="text-orange-400" />
                <span className="text-slate-300">{COMPANY_INFO.phone}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail size={16} className="text-orange-400" />
                <span className="text-slate-300">{COMPANY_INFO.email}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin size={16} className="text-orange-400" />
                <span className="text-slate-300">{COMPANY_INFO.address}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Enlaces Rápidos</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/" 
                  className="text-slate-300 hover:text-orange-400 transition-colors"
                >
                  Inicio
                </Link>
              </li>
              <li>
                <Link 
                  to="/productos" 
                  className="text-slate-300 hover:text-orange-400 transition-colors"
                >
                  Productos
                </Link>
              </li>
              <li>
                <Link 
                  to="/nosotros" 
                  className="text-slate-300 hover:text-orange-400 transition-colors"
                >
                  Nosotros
                </Link>
              </li>
              <li>
                <Link 
                  to="/contacto" 
                  className="text-slate-300 hover:text-orange-400 transition-colors"
                >
                  Contacto
                </Link>
              </li>
              <li>
                <Link 
                  to="/tienda" 
                  className="text-slate-300 hover:text-orange-400 transition-colors"
                >
                  Tienda Online
                </Link>
              </li>
            </ul>
          </div>

          {/* Social Media */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Síguenos</h3>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-slate-300 hover:text-blue-400 transition-colors"
                aria-label="Facebook"
              >
                <FaFacebook size={24} />
              </a>
              <a
                href="#"
                className="text-slate-300 hover:text-pink-400 transition-colors"
                aria-label="Instagram"
              >
                <FaInstagram size={24} />
              </a>
              <a
                href="#"
                className="text-slate-300 hover:text-blue-400 transition-colors"
                aria-label="LinkedIn"
              >
                <FaLinkedin size={24} />
              </a>
              <a
                href={`https://wa.me/${COMPANY_INFO.whatsapp}`}
                className="text-slate-300 hover:text-green-400 transition-colors"
                aria-label="WhatsApp"
              >
                <FaWhatsapp size={24} />
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-700 mt-8 pt-8 text-center">
          <p className="text-slate-400">
            © {currentYear} Hidroimplementos SPA. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </footer>
  );
};
