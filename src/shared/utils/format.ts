/**
 * Format currency in Chilean Pesos
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'CLP',
    minimumFractionDigits: 0,
  }).format(amount);
};

/**
 * Format flow rate with units
 */
export const formatFlowRate = (value: number, unit: string = 'L/min'): string => {
  return `${value.toLocaleString('es-CL')} ${unit}`;
};

/**
 * Format pressure with units
 */
export const formatPressure = (value: number, unit: string = 'bar'): string => {
  return `${value.toLocaleString('es-CL')} ${unit}`;
};

/**
 * Format power with units
 */
export const formatPower = (value: number, unit: string = 'kW'): string => {
  return `${value.toLocaleString('es-CL')} ${unit}`;
};

/**
 * Format phone number for WhatsApp
 */
export const formatPhoneForWhatsApp = (phone: string): string => {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Add country code if not present
  if (cleaned.startsWith('56')) {
    return cleaned;
  }
  
  return `56${cleaned}`;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};
