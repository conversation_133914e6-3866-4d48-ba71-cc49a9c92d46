/**
 * Validation utilities for forms
 */

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+?56)?[2-9]\d{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

export const isValidRUT = (rut: string): boolean => {
  // Remove dots and hyphens
  const cleanRUT = rut.replace(/[.-]/g, '');
  
  if (cleanRUT.length < 8 || cleanRUT.length > 9) {
    return false;
  }
  
  const body = cleanRUT.slice(0, -1);
  const dv = cleanRUT.slice(-1).toLowerCase();
  
  // Calculate verification digit
  let sum = 0;
  let multiplier = 2;
  
  for (let i = body.length - 1; i >= 0; i--) {
    sum += parseInt(body[i]) * multiplier;
    multiplier = multiplier === 7 ? 2 : multiplier + 1;
  }
  
  const remainder = sum % 11;
  const calculatedDV = remainder < 2 ? remainder.toString() : (11 - remainder === 10 ? 'k' : (11 - remainder).toString());
  
  return dv === calculatedDV;
};

export const validateRequired = (value: string | number): string | null => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return 'Este campo es requerido';
  }
  return null;
};

export const validateMinLength = (value: string, minLength: number): string | null => {
  if (value.length < minLength) {
    return `Debe tener al menos ${minLength} caracteres`;
  }
  return null;
};

export const validateMaxLength = (value: string, maxLength: number): string | null => {
  if (value.length > maxLength) {
    return `No puede exceder ${maxLength} caracteres`;
  }
  return null;
};

export const validateNumericRange = (value: number, min: number, max: number): string | null => {
  if (value < min || value > max) {
    return `El valor debe estar entre ${min} y ${max}`;
  }
  return null;
};
