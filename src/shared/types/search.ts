import { Product, ProductFilter } from './product';

export interface SearchParams {
  query?: string;
  filters?: ProductFilter;
  sortBy?: 'relevance' | 'price-asc' | 'price-desc' | 'name' | 'brand';
  page?: number;
  limit?: number;
}

export interface SearchResult {
  products: Product[];
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface AdvancedSearchParams {
  // Application details
  application: string;
  liquidType: string;
  
  // Flow requirements
  flowRate: number;
  flowRateUnit: 'L/min' | 'L/h' | 'm³/h';
  
  // Pressure requirements
  totalHead: number;
  totalHeadUnit: 'mca' | 'bar' | 'psi';
  
  // Power and electrical
  powerSupply: '220V Monofásico' | '380V Trifásico' | 'Otro';
  maxPower?: number;
  maxPowerUnit: 'kW' | 'HP';
  
  // Installation details
  installationType: 'Superficie' | 'Sumergible' | 'Inline' | 'Booster';
  suctionHeight?: number;
  suctionHeightUnit: 'm' | 'ft';
  
  // Environmental conditions
  ambientTemperature?: number;
  liquidTemperature?: number;
  temperatureUnit: '°C' | '°F';
  
  // Physical constraints
  spaceConstraints?: string;
  noiseRequirements?: 'Bajo' | 'Medio' | 'Alto';
  
  // Budget and preferences
  budgetRange?: {
    min: number;
    max: number;
    currency: 'CLP' | 'USD';
  };
  preferredBrands?: string[];
  
  // Additional requirements
  certifications?: string[];
  efficiency?: 'Estándar' | 'Alta' | 'Premium';
  maintenance?: 'Bajo' | 'Medio' | 'Alto';
  
  // Contact information
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
  company?: string;
  location?: string;
  urgency?: 'Inmediato' | '1-2 semanas' | '1 mes' | 'Flexible';
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'product' | 'brand' | 'category' | 'specification';
  category?: string;
  count?: number;
}

export interface SearchHistory {
  id: string;
  query: string;
  filters?: ProductFilter;
  timestamp: Date;
  resultCount: number;
}
