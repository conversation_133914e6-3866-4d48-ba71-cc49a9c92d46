export interface Product {
  id: string;
  name: string;
  brand: string;
  model: string;
  category: ProductCategory;
  description: string;
  image: string;
  price?: number;
  specifications: ProductSpecifications;
  availability: 'in-stock' | 'out-of-stock' | 'on-order';
  tags: string[];
}

export interface ProductSpecifications {
  // Pump specifications
  flowRate?: {
    min: number;
    max: number;
    unit: 'L/min' | 'L/h' | 'm³/h';
  };
  pressure?: {
    min: number;
    max: number;
    unit: 'bar' | 'psi' | 'mca';
  };
  power?: {
    value: number;
    unit: 'kW' | 'HP';
  };
  voltage?: {
    value: number;
    unit: 'V';
    phases: 1 | 3;
  };
  efficiency?: number; // percentage
  
  // Physical specifications
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'mm' | 'cm' | 'm';
  };
  weight?: {
    value: number;
    unit: 'kg' | 'g';
  };
  
  // Material specifications
  material?: string;
  sealType?: string;
  bearingType?: string;
  
  // Connection specifications
  inletSize?: string;
  outletSize?: string;
  connectionType?: string;
  
  // Operating conditions
  temperatureRange?: {
    min: number;
    max: number;
    unit: '°C' | '°F';
  };
  liquidType?: string[];
  
  // Additional specifications
  certifications?: string[];
  warranty?: string;
  origin?: string;
}

export type ProductCategory = 
  | 'centrifugal-pumps'
  | 'submersible-pumps'
  | 'peripheral-pumps'
  | 'self-priming-pumps'
  | 'booster-pumps'
  | 'irrigation-pumps'
  | 'industrial-pumps'
  | 'domestic-pumps'
  | 'accessories'
  | 'spare-parts';

export interface ProductFilter {
  category?: ProductCategory[];
  brand?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  flowRate?: {
    min: number;
    max: number;
  };
  pressure?: {
    min: number;
    max: number;
  };
  power?: {
    min: number;
    max: number;
  };
  availability?: ('in-stock' | 'out-of-stock' | 'on-order')[];
  tags?: string[];
}
