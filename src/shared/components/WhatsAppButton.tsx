import { AnimatePresence, motion } from 'framer-motion';
import { X, User } from 'lucide-react';
import React, { useState } from 'react';
import { FaWhatsapp } from 'react-icons/fa';

export const WhatsAppButton: React.FC = () => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [showNameInput, setShowNameInput] = useState(false);
  const [userName, setUserName] = useState('');

  const handleClick = () => {
    if (!userName.trim()) {
      setShowNameInput(true);
      setShowTooltip(false);
      return;
    }

    const phoneNumber = '56996512719'; // Número actualizado de Bastián
    const message = `¡Hola! Soy ${userName} y necesito ayuda con sistemas de riego e implementos hidráulicos. ¿Podrían asesorarme?`;
    window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank');
    setShowNameInput(false);
    setUserName('');
  };

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userName.trim()) {
      handleClick();
    }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {/* Formulario de nombre */}
        {showNameInput && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="absolute bottom-20 right-0 bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-6 border border-slate-200 dark:border-slate-700 backdrop-blur-sm min-w-[280px]"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <FaWhatsapp className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-sm font-semibold text-slate-900 dark:text-white">
                  Habla con Bastián
                </span>
              </div>
              <button
                onClick={() => setShowNameInput(false)}
                className="p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full transition-colors"
              >
                <X className="w-4 h-4 text-slate-500" />
              </button>
            </div>

            <form onSubmit={handleNameSubmit} className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-2">
                  ¿Cómo te llamas?
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <input
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    placeholder="Tu nombre"
                    className="w-full pl-10 pr-4 py-3 border border-slate-200 dark:border-slate-600 rounded-xl bg-slate-50 dark:bg-slate-700 text-slate-900 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                    autoFocus
                  />
                </div>
              </div>

              <motion.button
                type="submit"
                disabled={!userName.trim()}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-slate-300 disabled:to-slate-400 text-white py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center space-x-2"
                whileHover={{ scale: userName.trim() ? 1.02 : 1 }}
                whileTap={{ scale: userName.trim() ? 0.98 : 1 }}
              >
                <FaWhatsapp className="w-4 h-4" />
                <span>Chatear con Bastián</span>
              </motion.button>
            </form>

            <div className="absolute bottom-0 right-8 transform translate-y-1/2 rotate-45 w-4 h-4 bg-white dark:bg-slate-800 border-r border-b border-slate-200 dark:border-slate-700"></div>
          </motion.div>
        )}

        {/* Tooltip informativo */}
        {showTooltip && !showNameInput && (
          <motion.div
            initial={{ opacity: 0, x: 20, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 20, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="absolute bottom-20 right-0 bg-white dark:bg-slate-800 rounded-xl shadow-xl p-4 whitespace-nowrap border border-slate-200 dark:border-slate-700 backdrop-blur-sm"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <FaWhatsapp className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-slate-900 dark:text-white">
                  ¡Habla con Bastián!
                </span>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  Experto en riego • Online
                </span>
              </div>
              <button
                onClick={() => setShowTooltip(false)}
                className="p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full transition-colors"
              >
                <X className="w-3 h-3 text-slate-500" />
              </button>
            </div>
            <div className="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-3 h-3 bg-white dark:bg-slate-800 border-r border-b border-slate-200 dark:border-slate-700"></div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        onClick={handleClick}
        onMouseEnter={() => !showNameInput && setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="bg-gradient-to-br from-green-500 via-green-600 to-green-700 hover:from-green-600 hover:via-green-700 hover:to-green-800 text-white p-5 rounded-full shadow-2xl hover:shadow-green-500/25 transition-all relative group overflow-hidden"
        whileHover={{
          scale: 1.1,
          rotate: [0, -5, 5, 0],
          boxShadow: "0 20px 40px rgba(34, 197, 94, 0.3)"
        }}
        whileTap={{ scale: 0.95 }}
        animate={{
          boxShadow: [
            "0 0 0 0 rgba(34, 197, 94, 0.4)",
            "0 0 0 20px rgba(34, 197, 94, 0)",
          ],
        }}
        transition={{
          boxShadow: {
            duration: 2,
            repeat: Infinity,
            ease: "easeOut"
          },
          hover: {
            type: "spring",
            stiffness: 300,
            damping: 15
          }
        }}
      >
        {/* Fondo animado */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 rounded-full"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{
            rotate: { duration: 8, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
          }}
        />

        {/* Icono principal */}
        <div className="relative z-10">
          <FaWhatsapp className="w-8 h-8 group-hover:scale-110 transition-transform duration-300" />
        </div>

        {/* Indicador de estado online */}
        <motion.div
          className="absolute -top-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-3 border-white shadow-lg z-20"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [1, 0.7, 1]
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Badge de Bastián */}
        <motion.div
          className="absolute -top-3 -left-3 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg z-20"
          initial={{ scale: 0, rotate: -45 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 400 }}
        >
          B
        </motion.div>

        {/* Efecto de pulso */}
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-green-400"
          animate={{
            scale: [1, 1.5, 2],
            opacity: [0.5, 0.2, 0]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeOut"
          }}
        />
      </motion.button>
    </div>
  );
};
