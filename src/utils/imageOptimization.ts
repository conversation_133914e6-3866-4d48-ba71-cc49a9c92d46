// Utility functions for image optimization

export const generateSrcSet = (baseUrl: string, sizes: number[] = [400, 600, 800, 1200]) => {
  return sizes
    .map(size => `${baseUrl}?auto=compress&cs=tinysrgb&w=${size} ${size}w`)
    .join(', ');
};

export const generateSizes = (breakpoints: { [key: string]: string } = {
  '(max-width: 640px)': '100vw',
  '(max-width: 1024px)': '50vw',
  default: '33vw'
}) => {
  const entries = Object.entries(breakpoints);
  const mediaQueries = entries.slice(0, -1).map(([query, size]) => `${query} ${size}`);
  const defaultSize = entries[entries.length - 1][1];
  
  return [...mediaQueries, defaultSize].join(', ');
};

export const getOptimizedImageUrl = (
  url: string, 
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
    fit?: 'crop' | 'scale' | 'fill';
  } = {}
) => {
  const {
    width = 800,
    height,
    quality = 85,
    format = 'webp',
    fit = 'crop'
  } = options;

  // For Pexels images, use their optimization parameters
  if (url.includes('pexels.com')) {
    let optimizedUrl = `${url}?auto=compress&cs=tinysrgb&w=${width}`;
    if (height) optimizedUrl += `&h=${height}`;
    if (fit === 'crop') optimizedUrl += '&fit=crop';
    return optimizedUrl;
  }

  // For other images, return as-is or implement other CDN optimizations
  return url;
};

export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export const preloadImages = async (urls: string[]): Promise<void[]> => {
  return Promise.all(urls.map(preloadImage));
};

// CSS filters for image enhancement
export const imageFilters = {
  enhance: 'contrast(1.1) brightness(1.05) saturate(1.1)',
  vivid: 'contrast(1.2) brightness(1.1) saturate(1.3)',
  sharp: 'contrast(1.15) brightness(1.02) saturate(1.05)',
  warm: 'contrast(1.1) brightness(1.08) saturate(1.2) hue-rotate(5deg)',
  cool: 'contrast(1.1) brightness(1.05) saturate(1.1) hue-rotate(-5deg)',
  dramatic: 'contrast(1.3) brightness(0.95) saturate(1.4)',
  vintage: 'contrast(1.1) brightness(1.1) saturate(0.8) sepia(0.3)',
  bw: 'grayscale(1) contrast(1.2)',
  soft: 'contrast(0.9) brightness(1.1) saturate(0.9) blur(0.5px)'
};

// Intersection Observer for lazy loading
export const createLazyLoadObserver = (
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
) => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
};