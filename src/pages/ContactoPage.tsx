import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  MessageCircle,
  Send,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export function ContactoPage() {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    mensaje: '',
    tipoConsulta: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simular envío
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus('success');
      
      // También enviar por WhatsApp
      const whatsappMessage = `Nueva consulta desde web:
Nombre: ${formData.nombre}
Email: ${formData.email}
Teléfono: ${formData.telefono}
Tipo: ${formData.tipoConsulta}
Mensaje: ${formData.mensaje}`;
      
      window.open(`https://wa.me/56912345678?text=${encodeURIComponent(whatsappMessage)}`, '_blank');
      
      // Reset form
      setTimeout(() => {
        setFormData({
          nombre: '',
          email: '',
          telefono: '',
          mensaje: '',
          tipoConsulta: ''
        });
        setSubmitStatus('idle');
      }, 3000);
    }, 2000);
  };

  const contactMethods = [
    {
      icon: Phone,
      title: 'Teléfono',
      value: '+56 9 1234 5678',
      description: 'Lunes a Viernes 8:00 - 18:00',
      action: 'tel:+56912345678',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: MessageCircle,
      title: 'WhatsApp',
      value: '+56 9 1234 5678',
      description: 'Respuesta inmediata',
      action: 'https://wa.me/56912345678?text=Hola! Me gustaría recibir información sobre sus servicios.',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Mail,
      title: 'Email',
      value: '<EMAIL>',
      description: 'Respuesta en 24 horas',
      action: 'mailto:<EMAIL>',
      color: 'from-gray-500 to-gray-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white dark:from-slate-900 dark:to-slate-800 py-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Contáctanos
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Estamos aquí para ayudarte con tu proyecto de riego. 
            Contáctanos por el medio que prefieras.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          
          {/* Formulario de Contacto */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Envíanos tu Consulta
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nombre *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.nombre}
                    onChange={(e) => setFormData({...formData, nombre: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tu nombre completo"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Teléfono *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.telefono}
                    onChange={(e) => setFormData({...formData, telefono: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+56 9 1234 5678"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Consulta
                </label>
                <select
                  value={formData.tipoConsulta}
                  onChange={(e) => setFormData({...formData, tipoConsulta: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Seleccionar tipo</option>
                  <option value="riego">Sistema de Riego</option>
                  <option value="bomba">Bomba de Agua</option>
                  <option value="mantencion">Mantención</option>
                  <option value="cotizacion">Cotización</option>
                  <option value="otro">Otro</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Mensaje *
                </label>
                <textarea
                  required
                  rows={4}
                  value={formData.mensaje}
                  onChange={(e) => setFormData({...formData, mensaje: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  placeholder="Cuéntanos sobre tu proyecto..."
                />
              </div>
              
              <button
                type="submit"
                disabled={isSubmitting || submitStatus === 'success'}
                className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-300 flex items-center justify-center gap-3 ${
                  submitStatus === 'success'
                    ? 'bg-green-600 text-white'
                    : isSubmitting
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transform hover:scale-105'
                }`}
              >
                {submitStatus === 'success' ? (
                  <>
                    <CheckCircle className="w-6 h-6" />
                    ¡Mensaje Enviado!
                  </>
                ) : isSubmitting ? (
                  <>
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <Send className="w-6 h-6" />
                    Enviar Mensaje
                  </>
                )}
              </button>
              
              {submitStatus === 'success' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4 text-green-800 dark:text-green-200"
                >
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">¡Mensaje enviado exitosamente!</span>
                  </div>
                  <p className="text-sm mt-1">
                    También se ha enviado una copia por WhatsApp. Te contactaremos pronto.
                  </p>
                </motion.div>
              )}
            </form>
          </motion.div>
          
          {/* Información de Contacto */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-8"
          >
            
            {/* Métodos de Contacto */}
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Otros Medios de Contacto
              </h2>
              
              {contactMethods.map((method, index) => {
                const Icon = method.icon;
                return (
                  <motion.a
                    key={index}
                    href={method.action}
                    target={method.action.startsWith('http') ? '_blank' : undefined}
                    rel={method.action.startsWith('http') ? 'noopener noreferrer' : undefined}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className={`block bg-gradient-to-r ${method.color} text-white p-6 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl`}
                  >
                    <div className="flex items-center gap-4">
                      <Icon className="w-8 h-8" />
                      <div>
                        <h3 className="text-lg font-bold">{method.title}</h3>
                        <p className="text-white/90">{method.value}</p>
                        <p className="text-white/70 text-sm">{method.description}</p>
                      </div>
                    </div>
                  </motion.a>
                );
              })}
            </div>
            
            {/* Ubicación */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="bg-white dark:bg-slate-800 rounded-2xl p-6 shadow-lg"
            >
              <div className="flex items-center gap-3 mb-4">
                <MapPin className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  Nuestra Ubicación
                </h3>
              </div>
              
              <div className="space-y-3 text-gray-600 dark:text-gray-300">
                <p className="font-medium text-gray-900 dark:text-white">
                  Av. Principal 123, Cholchol
                </p>
                <p>Región de La Araucanía, Chile</p>
                
                <div className="flex items-center gap-2 pt-2">
                  <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Horarios</p>
                    <p className="text-sm">Lun-Vie: 8:00-18:00 | Sáb: 9:00-13:00</p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">Emergencias: 24/7</p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* Cobertura */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0 }}
              className="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6 rounded-2xl"
            >
              <h3 className="text-xl font-bold mb-3">Cobertura de Servicio</h3>
              <p className="mb-2">📍 Toda La Región de La Araucanía</p>
              <p className="mb-2">🚛 Instalación y mantención a domicilio</p>
              <p className="text-green-100">⚡ Servicio de emergencia 24/7</p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
