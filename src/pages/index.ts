// Lazy-loaded pages for code splitting
import { lazy } from 'react';

export const HomePage = lazy(() => import('./HomePage').then(m => ({ default: m.HomePage })));
export const ProductsPage = lazy(() => import('./ProductsPage').then(m => ({ default: m.ProductsPage })));
export const AboutPage = lazy(() => import('./AboutPage').then(m => ({ default: m.AboutPage })));
export const ContactPage = lazy(() => import('./ContactPage').then(m => ({ default: m.ContactPage })));
export const ShopPage = lazy(() => import('./ShopPage').then(m => ({ default: m.ShopPage })));
