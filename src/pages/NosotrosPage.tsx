import { motion } from 'framer-motion';
import { 
  MapPin, 
  Users, 
  Award, 
  Wrench, 
  Droplets, 
  Mountain,
  Clock,
  CheckCircle
} from 'lucide-react';

export function NosotrosPage() {
  const ventajas = [
    {
      icon: MapPin,
      title: 'Presencia Local',
      description: 'Ubicados en Cholchol, conocemos cada rincón de La Araucanía'
    },
    {
      icon: Mountain,
      title: 'Clima Araucano',
      description: '25+ años adaptando soluciones al clima único de la región'
    },
    {
      icon: Wrench,
      title: 'Instalación Presencial',
      description: 'Nuestro equipo técnico se desplaza a tu ubicación'
    },
    {
      icon: Droplets,
      title: 'Especialistas en Riego',
      description: 'Desde pequeños jardines hasta grandes cultivos'
    }
  ];

  const estadisticas = [
    { numero: '25+', texto: 'Años de Experiencia' },
    { numero: '500+', texto: 'Proyectos Instalados' },
    { numero: '50+', texto: '<PERSON><PERSON><PERSON>' },
    { numero: '100%', texto: 'Satisfacción Cliente' }
  ];

  const hitos = [
    {
      año: '1998',
      titulo: 'Fundación en Cholchol',
      descripcion: 'Iniciamos como un pequeño taller familiar especializado en reparación de bombas'
    },
    {
      año: '2005',
      titulo: 'Expansión Regional',
      descripcion: 'Comenzamos a atender toda La Araucanía con instalaciones de riego tecnificado'
    },
    {
      año: '2012',
      titulo: 'Certificación Técnica',
      descripcion: 'Obtuvimos certificaciones de las principales marcas internacionales'
    },
    {
      año: '2020',
      titulo: 'Innovación Digital',
      descripcion: 'Incorporamos tecnología digital para mejorar nuestros servicios'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 dark:from-slate-900 dark:to-slate-800 py-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <MapPin className="w-4 h-4" />
            Cholchol, Región de La Araucanía
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
              25+ Años
            </span>
            <br />
            Desde Cholchol
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Somos una empresa familiar que ha crecido junto a La Araucanía, 
            especializándonos en soluciones hídricas que entienden el clima y 
            las necesidades únicas de nuestra región.
          </p>
        </motion.div>

        {/* Estadísticas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
        >
          {estadisticas.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                {stat.numero}
              </div>
              <div className="text-gray-600 dark:text-gray-400 text-sm md:text-base">
                {stat.texto}
              </div>
            </div>
          ))}
        </motion.div>

        {/* Ventajas Locales */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
            Nuestra Ventaja Local
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {ventajas.map((ventaja, index) => {
              const Icon = ventaja.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg text-center hover:shadow-xl transition-shadow"
                >
                  <Icon className="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {ventaja.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {ventaja.descripcion}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Historia */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
            Nuestra Historia
          </h2>
          
          <div className="space-y-8">
            {hitos.map((hito, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className={`flex flex-col md:flex-row items-center gap-8 ${
                  index % 2 === 1 ? 'md:flex-row-reverse' : ''
                }`}
              >
                <div className="flex-1 bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {hito.año}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {hito.titulo}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {hito.descripcion}
                  </p>
                </div>
                
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Equipo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg mb-16"
        >
          <div className="text-center mb-8">
            <Users className="w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Nuestro Equipo
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Profesionales que conocen cada proyecto de riego en la región
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wrench className="w-10 h-10 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                Técnicos Especialistas
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Certificados en las principales marcas internacionales
              </p>
            </div>
            
            <div>
              <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-10 h-10 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                Ingenieros Agrónomos
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Especialistas en diseño de sistemas de riego eficientes
              </p>
            </div>
            
            <div>
              <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-10 h-10 text-orange-600 dark:text-orange-400" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                Servicio 24/7
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Atención de emergencias en toda La Araucanía
              </p>
            </div>
          </div>
        </motion.div>

        {/* Ubicación */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="text-center"
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            Nos Encuentras En
          </h2>
          
          <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg">
            <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400 mb-4">
              <MapPin className="w-6 h-6" />
              <span className="text-xl font-bold">Dirección Principal</span>
            </div>
            
            <p className="text-lg text-gray-900 dark:text-white mb-2">
              Av. Principal 123, Cholchol
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Región de La Araucanía, Chile
            </p>
            
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div>
                <h4 className="font-bold text-gray-900 dark:text-white mb-2">
                  Horarios de Atención
                </h4>
                <p className="text-gray-600 dark:text-gray-300">
                  Lunes a Viernes: 8:00 - 18:00<br />
                  Sábados: 9:00 - 13:00<br />
                  Emergencias: 24/7
                </p>
              </div>
              
              <div>
                <h4 className="font-bold text-gray-900 dark:text-white mb-2">
                  Cobertura de Servicio
                </h4>
                <p className="text-gray-600 dark:text-gray-300">
                  Toda La Araucanía<br />
                  Desde Temuco hasta Valdivia<br />
                  Instalación y mantención
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
