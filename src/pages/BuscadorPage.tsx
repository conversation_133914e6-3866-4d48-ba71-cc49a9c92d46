import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  Sprout, 
  Factory, 
  Droplets, 
  Settings, 
  Search,
  MessageCircle,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';

type SearchMode = 'simple' | 'advanced';
type PumpType = 'casa' | 'riego' | 'industrial' | 'achique';

export function BuscadorPage() {
  const [searchMode, setSearchMode] = useState<SearchMode>('simple');
  const [selectedPumpType, setSelectedPumpType] = useState<PumpType | null>(null);
  const [showExpertContact, setShowExpertContact] = useState(false);
  const [advancedParams, setAdvancedParams] = useState({
    aplicacion: '',
    tipoSistema: '',
    alturaTotal: '',
    caudalTotal: '',
    temperatura: '',
    fluido: '',
    controlVelocidad: ''
  });

  const pumpTypes = [
    {
      id: 'casa' as PumpType,
      icon: Home,
      title: 'Bomba para Casa',
      description: 'Presión de agua, piscinas, uso doméstico',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'riego' as PumpType,
      icon: Sprout,
      title: 'Bomba para Riego',
      description: 'Jardines, cultivos, sistemas de goteo',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'industrial' as PumpType,
      icon: Factory,
      title: 'Bomba Industrial',
      description: 'Procesos industriales, enfriamiento',
      color: 'from-gray-500 to-gray-600'
    },
    {
      id: 'achique' as PumpType,
      icon: Droplets,
      title: 'Bomba de Achique',
      description: 'Drenar, evacuar, emergencias',
      color: 'from-red-500 to-red-600'
    }
  ];

  const handleSimpleSearch = (type: PumpType) => {
    setSelectedPumpType(type);
    // Simular búsqueda - en este caso siempre va a expertos
    setTimeout(() => {
      setShowExpertContact(true);
    }, 1000);
  };

  const handleAdvancedSearch = () => {
    // Simular búsqueda avanzada - también va a expertos
    setTimeout(() => {
      setShowExpertContact(true);
    }, 1000);
  };

  const getWhatsAppMessage = () => {
    if (searchMode === 'simple' && selectedPumpType) {
      const typeNames = {
        casa: 'casa/doméstico',
        riego: 'riego/jardín',
        industrial: 'industrial',
        achique: 'achique/drenaje'
      };
      return `Hola! Necesito una bomba para ${typeNames[selectedPumpType]}. ¿Pueden asesorarme?`;
    } else if (searchMode === 'advanced') {
      const params = Object.entries(advancedParams)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      return `Hola! Busco bomba con estas especificaciones: ${params}. ¿Tienen algo similar o recomendaciones?`;
    }
    return 'Hola! Necesito asesoría para encontrar la bomba ideal para mi proyecto. ¿Pueden ayudarme?';
  };

  if (showExpertContact) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 dark:from-slate-900 dark:to-slate-800 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              ¡Perfecto! Nuestros expertos tienen la solución
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-2">
              Cada proyecto de riego es único. Déjanos diseñar la solución perfecta para ti.
            </p>
            <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400 mt-4">
              <MapPin className="w-5 h-5" />
              <span className="font-medium">Visita técnica gratuita en La Araucanía</span>
            </div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {/* WhatsApp */}
            <motion.a
              href={`https://wa.me/56912345678?text=${encodeURIComponent(getWhatsAppMessage())}`}
              target="_blank"
              rel="noopener noreferrer"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-[#25D366] hover:bg-[#128C7E] text-white p-8 rounded-2xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              <MessageCircle className="w-12 h-12 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-2">💬 Hablar con Experto</h3>
              <p className="text-green-100">Respuesta inmediata por WhatsApp</p>
            </motion.a>

            {/* Teléfono */}
            <motion.a
              href="tel:+56912345678"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-blue-600 hover:bg-blue-700 text-white p-8 rounded-2xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              <Phone className="w-12 h-12 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-2">📞 Llamar Ahora</h3>
              <p className="text-blue-100">Atención directa de lunes a viernes</p>
            </motion.a>

            {/* Email */}
            <motion.a
              href="mailto:<EMAIL>"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-gray-600 hover:bg-gray-700 text-white p-8 rounded-2xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              <Mail className="w-12 h-12 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-2">📧 Enviar Consulta</h3>
              <p className="text-gray-100">Respuesta en 24 horas</p>
            </motion.a>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="bg-white dark:bg-slate-800 rounded-2xl p-8 text-center shadow-lg"
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ventaja Local
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              "Instalamos sistemas desde Temuco hasta Valdivia"
            </p>
            <button
              onClick={() => setShowExpertContact(false)}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              ← Volver al buscador
            </button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white dark:from-slate-900 dark:to-slate-800 py-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Encuentra tu bomba ideal
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Te ayudamos a encontrar la solución perfecta para tu proyecto
          </p>
        </motion.div>

        {/* Toggle de modo */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="flex justify-center mb-8"
        >
          <div className="bg-white dark:bg-slate-800 rounded-xl p-2 shadow-lg">
            <button
              onClick={() => setSearchMode('simple')}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                searchMode === 'simple'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'
              }`}
            >
              Búsqueda Simple
            </button>
            <button
              onClick={() => setSearchMode('advanced')}
              className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center gap-2 ${
                searchMode === 'advanced'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'
              }`}
            >
              <Settings className="w-4 h-4" />
              Búsqueda Técnica Avanzada
            </button>
          </div>
        </motion.div>

        <AnimatePresence mode="wait">
          {searchMode === 'simple' ? (
            <motion.div
              key="simple"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid md:grid-cols-2 gap-6"
            >
              {pumpTypes.map((type, index) => {
                const Icon = type.icon;
                return (
                  <motion.button
                    key={type.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => handleSimpleSearch(type.id)}
                    className={`bg-gradient-to-r ${type.color} text-white p-8 rounded-2xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl`}
                  >
                    <Icon className="w-12 h-12 mb-4" />
                    <h3 className="text-2xl font-bold mb-2">{type.title}</h3>
                    <p className="text-white/90">{type.description}</p>
                  </motion.button>
                );
              })}
            </motion.div>
          ) : (
            <motion.div
              key="advanced"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg"
            >
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Especificaciones Técnicas
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Aplicación
                  </label>
                  <select
                    value={advancedParams.aplicacion}
                    onChange={(e) => setAdvancedParams({...advancedParams, aplicacion: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Seleccionar aplicación</option>
                    <option value="riego">Riego</option>
                    <option value="presion">Presión doméstica</option>
                    <option value="industrial">Industrial</option>
                    <option value="achique">Achique</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Caudal Total (L/min)
                  </label>
                  <input
                    type="number"
                    value={advancedParams.caudalTotal}
                    onChange={(e) => setAdvancedParams({...advancedParams, caudalTotal: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                    placeholder="Ej: 100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Altura Total (m)
                  </label>
                  <input
                    type="number"
                    value={advancedParams.alturaTotal}
                    onChange={(e) => setAdvancedParams({...advancedParams, alturaTotal: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                    placeholder="Ej: 25"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Temperatura (°C)
                  </label>
                  <input
                    type="number"
                    value={advancedParams.temperatura}
                    onChange={(e) => setAdvancedParams({...advancedParams, temperatura: e.target.value})}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                    placeholder="Ej: 20"
                  />
                </div>
              </div>

              <button
                onClick={handleAdvancedSearch}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-3"
              >
                <Search className="w-6 h-6" />
                Buscar Bomba Técnica
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
