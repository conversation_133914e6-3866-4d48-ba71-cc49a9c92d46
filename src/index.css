/* Fuente Inter optimizada */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Importar componentes generados con Tailwind Generator */
/* @import './styles/tailwind-generator-components.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animaciones personalizadas para Hidroimplementos */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

/* Variables CSS para temas */
:root {
  --hidro-blue-primary: #2563eb;
  --hidro-turquoise-primary: #06b6d4;
  --hidro-coral-primary: #f97316;
  --hidro-lime-primary: #84cc16;
}

/* Estilos base para el body */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-hidro-blue-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-hidro-blue-500;
}
