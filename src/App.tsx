import { useEffect, useState } from 'react';
import { AdvancedPumpFinder } from './components/AdvancedPumpFinder';
import { Footer } from './components/Footer';
import { HeroSection } from './components/HeroSection';
import { LoadingScreen } from './components/LoadingScreen';
import { Navbar } from './components/Navbar';
import { PhoneButton } from './components/PhoneButton';
import { ProductGrid } from './components/ProductGrid';
import { SmartSearch } from './components/sections/SmartSearch';
import { SEO } from './components/SEO';
import { ToastProvider } from './components/ToastProvider';
import { ValueSection } from './components/ValueSection';
import { WhatsAppButton } from './components/WhatsAppButton';
import { useImagePreloader } from './hooks/useImagePreloader';

// Critical images to preload
const criticalImages = [
  'https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1200',
  'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
  'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800'
];

function App() {
  const [appLoaded, setAppLoaded] = useState(false);
  const { isLoading, progress } = useImagePreloader({
    images: criticalImages,
    priority: true
  });

  useEffect(() => {
    // Simulate additional loading time for app initialization
    const timer = setTimeout(() => {
      if (!isLoading) {
        setAppLoaded(true);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [isLoading]);

  return (
    <ToastProvider>
      <div className="min-h-screen bg-white dark:bg-slate-900 transition-colors">
        <LoadingScreen
          isLoading={!appLoaded}
          progress={progress}
          onComplete={() => setAppLoaded(true)}
        />

        {appLoaded && (
          <>
            <SEO />
            <Navbar />
            <main>
              <HeroSection />
              <SmartSearch />
              <AdvancedPumpFinder />
              <ProductGrid />
              <ValueSection />
            </main>
            <Footer />
            <WhatsAppButton />
            <PhoneButton />
          </>
        )}
      </div>
    </ToastProvider>
  );
}

export default App;