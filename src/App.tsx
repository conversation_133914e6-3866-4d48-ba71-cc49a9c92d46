import { useEffect, useState } from 'react';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import { Footer } from './components/Footer';
import { LoadingScreen } from './components/LoadingScreen';
import { Navbar } from './components/Navbar';
import { SEO } from './components/SEO';
import { ToastProvider } from './components/ToastProvider';
import { WhatsAppButton } from './components/WhatsAppButton';
import { useImagePreloader } from './hooks/useImagePreloader';

// Páginas
import { BuscadorPage } from './pages/BuscadorPage';
import { ContactoPage } from './pages/ContactoPage';
import { HomePage } from './pages/HomePage';
import { NosotrosPage } from './pages/NosotrosPage';

// Critical images to preload - usando imágenes locales
const criticalImages = [
  '/bomba-centrifuga-ksb.webp',
  '/bomba-sumergible-grundfos.webp',
  '/sistema-goteo-premium.webp',
  '/aspersor-rotativo.webp',
  '/valvula-solenoide-hunter.webp'
];

function App() {
  const [appLoaded, setAppLoaded] = useState(false);
  const { isLoading, progress } = useImagePreloader({
    images: criticalImages,
    priority: true
  });

  useEffect(() => {
    // Simulate additional loading time for app initialization
    const timer = setTimeout(() => {
      if (!isLoading) {
        setAppLoaded(true);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [isLoading]);

  return (
    <ToastProvider>
      <div className="min-h-screen bg-white dark:bg-slate-900 transition-colors">
        <LoadingScreen
          isLoading={!appLoaded}
          progress={progress}
          onComplete={() => setAppLoaded(true)}
        />

        {appLoaded && (
          <Router>
            <SEO />
            <Navbar />
            <main>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/buscador" element={<BuscadorPage />} />
                <Route path="/nosotros" element={<NosotrosPage />} />
                <Route path="/contacto" element={<ContactoPage />} />
              </Routes>
            </main>
            <Footer />
            <WhatsAppButton />
          </Router>
        )}
      </div>
    </ToastProvider>
  );
}

export default App;