import { useState, useEffect } from 'react';

interface UseImagePreloaderProps {
  images: string[];
  priority?: boolean;
}

export const useImagePreloader = ({ images, priority = false }: UseImagePreloaderProps) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (images.length === 0) {
      setIsLoading(false);
      return;
    }

    const imagePromises = images.map((src) => {
      return new Promise<string>((resolve, reject) => {
        const img = new Image();
        
        // Set loading priority
        if (priority) {
          img.loading = 'eager';
        }
        
        img.onload = () => {
          setLoadedImages(prev => new Set([...prev, src]));
          setProgress(prev => prev + (100 / images.length));
          resolve(src);
        };
        
        img.onerror = () => {
          console.warn(`Failed to load image: ${src}`);
          setProgress(prev => prev + (100 / images.length));
          reject(src);
        };
        
        img.src = src;
      });
    });

    Promise.allSettled(imagePromises).then(() => {
      setIsLoading(false);
    });
  }, [images, priority]);

  return {
    loadedImages,
    isLoading,
    progress: Math.round(progress),
    isImageLoaded: (src: string) => loadedImages.has(src)
  };
};