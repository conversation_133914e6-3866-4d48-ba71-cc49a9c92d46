import { useState, useEffect } from 'react';

interface UseImagePreloaderProps {
  images: string[];
  priority?: boolean;
}

export const useImagePreloader = ({ images, priority = false }: UseImagePreloaderProps) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (images.length === 0) {
      setIsLoading(false);
      setProgress(100);
      return;
    }

    let completedImages = 0;

    const imagePromises = images.map((src) => {
      return new Promise<string>((resolve, reject) => {
        const img = new Image();

        // Set loading priority
        if (priority) {
          img.loading = 'eager';
        }

        img.onload = () => {
          setLoadedImages(prev => new Set([...prev, src]));
          completedImages++;
          const newProgress = Math.round((completedImages / images.length) * 100);
          setProgress(Math.min(newProgress, 100));
          resolve(src);
        };

        img.onerror = () => {
          console.warn(`Failed to load image: ${src}`);
          completedImages++;
          const newProgress = Math.round((completedImages / images.length) * 100);
          setProgress(Math.min(newProgress, 100));
          reject(src);
        };

        img.src = src;
      });
    });

    Promise.allSettled(imagePromises).then(() => {
      setProgress(100);
      setIsLoading(false);
    });
  }, [images, priority]);

  return {
    loadedImages,
    isLoading,
    progress: Math.round(progress),
    isImageLoaded: (src: string) => loadedImages.has(src)
  };
};