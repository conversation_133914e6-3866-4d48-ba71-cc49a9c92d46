// Main component
export { ProductCatalog } from './components';

// Individual components
export {
  ProductGrid,
  ProductCard,
  SearchBar,
  FilterPanel,
  SortControl,
  Pagination
} from './components';

// Types
export type {
  CatalogState,
  ProductGridProps,
  ProductCardProps,
  FilterPanelProps,
  SearchBarProps,
  SortControlProps,
  PaginationProps
} from './types';

// Hooks
export { useCatalog } from './hooks';

// Services
export { fetchProducts, getProductById, getFeaturedProducts } from './services';
