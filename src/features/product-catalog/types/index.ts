import { Product, ProductFilter, SearchParams, SearchResult } from '@shared/types';

export interface CatalogState {
  products: Product[];
  filteredProducts: Product[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  filters: ProductFilter;
  searchQuery: string;
  sortBy: 'relevance' | 'price-asc' | 'price-desc' | 'name' | 'brand';
}

export interface ProductGridProps {
  products: Product[];
  isLoading?: boolean;
  onProductSelect?: (product: Product) => void;
}

export interface ProductCardProps {
  product: Product;
  onSelect?: (product: Product) => void;
  onWhatsAppConsult?: (product: Product) => void;
}

export interface FilterPanelProps {
  filters: ProductFilter;
  onFiltersChange: (filters: ProductFilter) => void;
  onReset: () => void;
}

export interface SearchBarProps {
  query: string;
  onQueryChange: (query: string) => void;
  onSearch: () => void;
  placeholder?: string;
}

export interface SortControlProps {
  sortBy: CatalogState['sortBy'];
  onSortChange: (sortBy: CatalogState['sortBy']) => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}
