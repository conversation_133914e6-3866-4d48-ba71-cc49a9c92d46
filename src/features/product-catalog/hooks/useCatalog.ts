import { useState, useCallback, useEffect } from 'react';
import { Product, ProductFilter, SearchParams } from '@shared/types';
import { CatalogState } from '../types';
import { fetchProducts } from '../services';

const initialState: CatalogState = {
  products: [],
  filteredProducts: [],
  isLoading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  filters: {},
  searchQuery: '',
  sortBy: 'relevance'
};

export const useCatalog = () => {
  const [state, setState] = useState<CatalogState>(initialState);

  const loadProducts = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const searchParams: SearchParams = {
        query: state.searchQuery,
        filters: state.filters,
        sortBy: state.sortBy,
        page: state.currentPage,
        limit: 12
      };

      const result = await fetchProducts(searchParams);
      
      setState(prev => ({
        ...prev,
        products: result.products,
        filteredProducts: result.products,
        totalPages: result.totalPages,
        isLoading: false
      }));
    } catch (error) {
      console.error('Error loading products:', error);
      setState(prev => ({
        ...prev,
        error: 'Error al cargar productos. Intenta nuevamente.',
        isLoading: false
      }));
    }
  }, [state.searchQuery, state.filters, state.sortBy, state.currentPage]);

  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1 // Reset to first page on new search
    }));
  }, []);

  const setFilters = useCallback((filters: ProductFilter) => {
    setState(prev => ({
      ...prev,
      filters,
      currentPage: 1 // Reset to first page on filter change
    }));
  }, []);

  const setSortBy = useCallback((sortBy: CatalogState['sortBy']) => {
    setState(prev => ({
      ...prev,
      sortBy,
      currentPage: 1 // Reset to first page on sort change
    }));
  }, []);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const resetFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {},
      searchQuery: '',
      sortBy: 'relevance',
      currentPage: 1
    }));
  }, []);

  const search = useCallback(() => {
    loadProducts();
  }, [loadProducts]);

  // Load products when dependencies change
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  return {
    ...state,
    setSearchQuery,
    setFilters,
    setSortBy,
    setCurrentPage,
    resetFilters,
    search,
    reload: loadProducts
  };
};
