import { Product, ProductFilter, SearchParams, SearchResult } from '@shared/types';

// Mock product database
const MOCK_PRODUCTS: Product[] = [
  {
    id: '1',
    name: 'KSB Etanorm 125-100-250',
    brand: 'KSB',
    model: 'Etanorm 125-100-250',
    category: 'centrifugal-pumps',
    description: 'Bomba centrífuga industrial de alta eficiencia para aplicaciones de agua limpia',
    image: '/bomba-centrifuga-ksb.webp',
    price: 2850000,
    specifications: {
      flowRate: { min: 50, max: 150, unit: 'm³/h' },
      pressure: { min: 20, max: 80, unit: 'mca' },
      power: { value: 15, unit: 'kW' },
      voltage: { value: 380, unit: 'V', phases: 3 },
      efficiency: 85,
      material: 'Hierro fundido',
      connectionType: 'Brida',
      temperatureRange: { min: 0, max: 80, unit: '°C' },
      liquidType: ['Agua limpia', 'Agua industrial'],
      certifications: ['ISO 9001', 'CE'],
      warranty: '2 años',
      origin: 'Alemania'
    },
    availability: 'in-stock',
    tags: ['industrial', 'alta-eficiencia', 'agua-limpia']
  },
  {
    id: '2',
    name: '<PERSON>rundfos SP 30-7',
    brand: 'Grundfos',
    model: 'SP 30-7',
    category: 'submersible-pumps',
    description: 'Bomba sumergible para pozos profundos y aplicaciones de abastecimiento',
    image: '/bomba-sumergible-grundfos.webp',
    price: 1850000,
    specifications: {
      flowRate: { min: 20, max: 80, unit: 'm³/h' },
      pressure: { min: 30, max: 120, unit: 'mca' },
      power: { value: 7.5, unit: 'kW' },
      voltage: { value: 380, unit: 'V', phases: 3 },
      efficiency: 82,
      material: 'Acero inoxidable',
      connectionType: 'Roscada',
      temperatureRange: { min: 0, max: 60, unit: '°C' },
      liquidType: ['Agua limpia', 'Agua de pozo'],
      certifications: ['ISO 9001', 'NSF'],
      warranty: '3 años',
      origin: 'Dinamarca'
    },
    availability: 'in-stock',
    tags: ['sumergible', 'pozo-profundo', 'abastecimiento']
  },
  {
    id: '3',
    name: 'Sistema Goteo Premium',
    brand: 'Hidroimplementos',
    model: 'SGP-2000',
    category: 'irrigation-pumps',
    description: 'Sistema completo de riego por goteo para cultivos intensivos',
    image: '/sistema-goteo-premium.webp',
    price: 450000,
    specifications: {
      flowRate: { min: 5, max: 25, unit: 'L/min' },
      pressure: { min: 1, max: 4, unit: 'bar' },
      power: { value: 0.5, unit: 'kW' },
      voltage: { value: 220, unit: 'V', phases: 1 },
      efficiency: 90,
      material: 'PVC y polietileno',
      connectionType: 'Rápida',
      temperatureRange: { min: 5, max: 40, unit: '°C' },
      liquidType: ['Agua limpia', 'Soluciones nutritivas'],
      certifications: ['ISO 14001'],
      warranty: '1 año',
      origin: 'Chile'
    },
    availability: 'in-stock',
    tags: ['riego', 'goteo', 'agricultura', 'eficiente']
  }
];

/**
 * Fetch products with filtering and pagination
 */
export const fetchProducts = async (params: SearchParams): Promise<SearchResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  let filteredProducts = [...MOCK_PRODUCTS];
  
  // Apply text search
  if (params.query) {
    const query = params.query.toLowerCase();
    filteredProducts = filteredProducts.filter(product =>
      product.name.toLowerCase().includes(query) ||
      product.brand.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query) ||
      product.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  // Apply filters
  if (params.filters) {
    const { category, brand, priceRange, availability } = params.filters;
    
    if (category && category.length > 0) {
      filteredProducts = filteredProducts.filter(product =>
        category.includes(product.category)
      );
    }
    
    if (brand && brand.length > 0) {
      filteredProducts = filteredProducts.filter(product =>
        brand.includes(product.brand)
      );
    }
    
    if (priceRange && product.price) {
      filteredProducts = filteredProducts.filter(product =>
        product.price! >= priceRange.min && product.price! <= priceRange.max
      );
    }
    
    if (availability && availability.length > 0) {
      filteredProducts = filteredProducts.filter(product =>
        availability.includes(product.availability)
      );
    }
  }
  
  // Apply sorting
  if (params.sortBy) {
    switch (params.sortBy) {
      case 'price-asc':
        filteredProducts.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case 'price-desc':
        filteredProducts.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case 'name':
        filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'brand':
        filteredProducts.sort((a, b) => a.brand.localeCompare(b.brand));
        break;
      default:
        // relevance - keep original order
        break;
    }
  }
  
  // Apply pagination
  const page = params.page || 1;
  const limit = params.limit || 12;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
  
  const totalPages = Math.ceil(filteredProducts.length / limit);
  
  return {
    products: paginatedProducts,
    total: filteredProducts.length,
    page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1
  };
};

/**
 * Get product by ID
 */
export const getProductById = async (id: string): Promise<Product | null> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return MOCK_PRODUCTS.find(product => product.id === id) || null;
};

/**
 * Get featured products
 */
export const getFeaturedProducts = async (limit: number = 6): Promise<Product[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return MOCK_PRODUCTS.slice(0, limit);
};
