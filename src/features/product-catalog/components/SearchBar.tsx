import React, { useState } from 'react';
import { Search, X } from 'lucide-react';
import { Button, Input } from '@shared/ui';
import { SearchBarProps } from '../types';

export const SearchBar: React.FC<SearchBarProps> = ({
  query,
  onQueryChange,
  onSearch,
  placeholder = 'Buscar productos, marcas, modelos...'
}) => {
  const [localQuery, setLocalQuery] = useState(query);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onQueryChange(localQuery);
    onSearch();
  };

  const handleClear = () => {
    setLocalQuery('');
    onQueryChange('');
    onSearch();
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="relative">
        <Input
          type="text"
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          placeholder={placeholder}
          className="pl-12 pr-20"
        />
        
        {/* Search Icon */}
        <Search 
          className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400" 
          size={20} 
        />
        
        {/* Clear Button */}
        {localQuery && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-16 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X size={20} />
          </button>
        )}
        
        {/* Search Button */}
        <Button
          type="submit"
          size="sm"
          className="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
          Buscar
        </Button>
      </div>
    </form>
  );
};
