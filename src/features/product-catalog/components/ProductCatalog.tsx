import React from 'react';
import { motion } from 'framer-motion';
import { SearchBar } from './SearchBar';
import { FilterPanel } from './FilterPanel';
import { SortControl } from './SortControl';
import { ProductGrid } from './ProductGrid';
import { Pagination } from './Pagination';
import { useCatalog } from '../hooks';

export const ProductCatalog: React.FC = () => {
  const {
    products,
    isLoading,
    error,
    currentPage,
    totalPages,
    filters,
    searchQuery,
    sortBy,
    setSearchQuery,
    setFilters,
    setSortBy,
    setCurrentPage,
    resetFilters,
    search
  } = useCatalog();

  return (
    <section className="py-20 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Catálogo de Productos
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            Explora nuestra amplia gama de bombas y sistemas de riego. 
            Encuentra el producto perfecto para tu proyecto.
          </p>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-8"
        >
          <SearchBar
            query={searchQuery}
            onQueryChange={setSearchQuery}
            onSearch={search}
          />
        </motion.div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:w-80 flex-shrink-0"
          >
            <FilterPanel
              filters={filters}
              onFiltersChange={setFilters}
              onReset={resetFilters}
            />
          </motion.div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Controls Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0"
            >
              <div className="text-sm text-slate-600 dark:text-slate-400">
                {!isLoading && (
                  <>
                    Mostrando {products.length} productos
                    {totalPages > 1 && ` (página ${currentPage} de ${totalPages})`}
                  </>
                )}
              </div>
              
              <SortControl
                sortBy={sortBy}
                onSortChange={setSortBy}
              />
            </motion.div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
              >
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </motion.div>
            )}

            {/* Product Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mb-8"
            >
              <ProductGrid
                products={products}
                isLoading={isLoading}
              />
            </motion.div>

            {/* Pagination */}
            {totalPages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="flex justify-center"
              >
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                />
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
