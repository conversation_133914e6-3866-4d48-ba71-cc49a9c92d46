import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';
import { Button, Input } from '@shared/ui';
import { PRODUCT_CATEGORIES, PUMP_BRANDS } from '@shared/constants';
import { FilterPanelProps } from '../types';

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFiltersChange,
  onReset
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    category: true,
    brand: false,
    price: false,
    availability: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category: string) => {
    const currentCategories = filters.category || [];
    const newCategories = currentCategories.includes(category as any)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category as any];
    
    onFiltersChange({
      ...filters,
      category: newCategories.length > 0 ? newCategories : undefined
    });
  };

  const handleBrandChange = (brand: string) => {
    const currentBrands = filters.brand || [];
    const newBrands = currentBrands.includes(brand)
      ? currentBrands.filter(b => b !== brand)
      : [...currentBrands, brand];
    
    onFiltersChange({
      ...filters,
      brand: newBrands.length > 0 ? newBrands : undefined
    });
  };

  const handlePriceRangeChange = (field: 'min' | 'max', value: string) => {
    const numValue = value ? parseInt(value) : undefined;
    const currentRange = filters.priceRange || { min: 0, max: 10000000 };
    
    onFiltersChange({
      ...filters,
      priceRange: {
        ...currentRange,
        [field]: numValue || (field === 'min' ? 0 : 10000000)
      }
    });
  };

  const handleAvailabilityChange = (availability: string) => {
    const currentAvailability = filters.availability || [];
    const newAvailability = currentAvailability.includes(availability as any)
      ? currentAvailability.filter(a => a !== availability)
      : [...currentAvailability, availability as any];
    
    onFiltersChange({
      ...filters,
      availability: newAvailability.length > 0 ? newAvailability : undefined
    });
  };

  const hasActiveFilters = Object.values(filters).some(filter => 
    Array.isArray(filter) ? filter.length > 0 : filter !== undefined
  );

  const FilterSection: React.FC<{
    title: string;
    section: string;
    children: React.ReactNode;
  }> = ({ title, section, children }) => (
    <div className="border-b border-slate-200 dark:border-slate-700 pb-4">
      <button
        onClick={() => toggleSection(section)}
        className="flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white mb-3"
      >
        {title}
        {expandedSections[section] ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
      </button>
      
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-4">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          variant="outline"
          leftIcon={<Filter size={16} />}
          className="w-full"
        >
          Filtros {hasActiveFilters && '(activos)'}
        </Button>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {(isOpen || window.innerWidth >= 1024) && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg lg:shadow-none"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Filtros
              </h3>
              <div className="flex items-center space-x-2">
                {hasActiveFilters && (
                  <Button
                    onClick={onReset}
                    variant="ghost"
                    size="sm"
                  >
                    Limpiar
                  </Button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="lg:hidden text-slate-400 hover:text-slate-600"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            <div className="space-y-6">
              {/* Categories */}
              <FilterSection title="Categorías" section="category">
                <div className="space-y-2">
                  {Object.entries(PRODUCT_CATEGORIES).map(([key, label]) => (
                    <label key={key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.category?.includes(key as any) || false}
                        onChange={() => handleCategoryChange(key)}
                        className="rounded border-slate-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {label}
                      </span>
                    </label>
                  ))}
                </div>
              </FilterSection>

              {/* Brands */}
              <FilterSection title="Marcas" section="brand">
                <div className="space-y-2">
                  {PUMP_BRANDS.map((brand) => (
                    <label key={brand} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.brand?.includes(brand) || false}
                        onChange={() => handleBrandChange(brand)}
                        className="rounded border-slate-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {brand}
                      </span>
                    </label>
                  ))}
                </div>
              </FilterSection>

              {/* Price Range */}
              <FilterSection title="Rango de precio" section="price">
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-slate-600 dark:text-slate-400 mb-1">
                      Precio mínimo
                    </label>
                    <Input
                      type="number"
                      placeholder="0"
                      value={filters.priceRange?.min || ''}
                      onChange={(e) => handlePriceRangeChange('min', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-slate-600 dark:text-slate-400 mb-1">
                      Precio máximo
                    </label>
                    <Input
                      type="number"
                      placeholder="Sin límite"
                      value={filters.priceRange?.max || ''}
                      onChange={(e) => handlePriceRangeChange('max', e.target.value)}
                    />
                  </div>
                </div>
              </FilterSection>

              {/* Availability */}
              <FilterSection title="Disponibilidad" section="availability">
                <div className="space-y-2">
                  {[
                    { key: 'in-stock', label: 'En stock' },
                    { key: 'out-of-stock', label: 'Agotado' },
                    { key: 'on-order', label: 'Por encargo' }
                  ].map(({ key, label }) => (
                    <label key={key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.availability?.includes(key as any) || false}
                        onChange={() => handleAvailabilityChange(key)}
                        className="rounded border-slate-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {label}
                      </span>
                    </label>
                  ))}
                </div>
              </FilterSection>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
