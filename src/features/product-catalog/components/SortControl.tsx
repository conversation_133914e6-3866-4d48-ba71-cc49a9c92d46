import React from 'react';
import { ArrowUpDown } from 'lucide-react';
import { SortControlProps } from '../types';

export const SortControl: React.FC<SortControlProps> = ({
  sortBy,
  onSortChange
}) => {
  const sortOptions = [
    { value: 'relevance', label: 'Relevan<PERSON>' },
    { value: 'name', label: 'Nombre A-Z' },
    { value: 'brand', label: 'Marca A-Z' },
    { value: 'price-asc', label: 'Precio: Menor a mayor' },
    { value: 'price-desc', label: 'Precio: Mayor a menor' }
  ] as const;

  return (
    <div className="flex items-center space-x-2">
      <ArrowUpDown size={16} className="text-slate-500" />
      <span className="text-sm text-slate-600 dark:text-slate-400">
        Ordenar por:
      </span>
      <select
        value={sortBy}
        onChange={(e) => onSortChange(e.target.value as any)}
        className="text-sm border border-slate-300 dark:border-slate-600 rounded-lg px-3 py-2 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
      >
        {sortOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};
