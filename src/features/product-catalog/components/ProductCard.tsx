import React from 'react';
import { motion } from 'framer-motion';
import { FaWhatsapp } from 'react-icons/fa';
import { Eye, ShoppingCart } from 'lucide-react';
import { Button } from '@shared/ui';
import { formatCurrency, formatPhoneForWhatsApp, truncateText } from '@shared/utils';
import { COMPANY_INFO } from '@shared/constants';
import { ProductCardProps } from '../types';

export const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  onSelect, 
  onWhatsAppConsult 
}) => {
  const handleWhatsAppClick = () => {
    if (onWhatsAppConsult) {
      onWhatsAppConsult(product);
    } else {
      const message = `Hola! Me interesa el producto ${product.name} (${product.brand}). ¿Pueden darme más información sobre disponibilidad y precio?`;
      const phoneNumber = formatPhoneForWhatsApp(COMPANY_INFO.phone);
      const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
      window.open(whatsappURL, '_blank');
    }
  };

  const handleViewDetails = () => {
    if (onSelect) {
      onSelect(product);
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in-stock':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'out-of-stock':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'on-order':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-700';
    }
  };

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in-stock':
        return 'En stock';
      case 'out-of-stock':
        return 'Agotado';
      case 'on-order':
        return 'Por encargo';
      default:
        return 'Consultar';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow overflow-hidden"
    >
      {/* Product Image */}
      <div className="relative aspect-square bg-slate-100 dark:bg-slate-700">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-product.jpg';
          }}
        />
        
        {/* Availability Badge */}
        <div className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(product.availability)}`}>
          {getAvailabilityText(product.availability)}
        </div>

        {/* Quick Actions Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
          <Button
            onClick={handleViewDetails}
            variant="secondary"
            size="sm"
            leftIcon={<Eye size={16} />}
          >
            Ver detalles
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-6">
        {/* Brand */}
        <div className="text-sm font-medium text-orange-600 dark:text-orange-400 mb-1">
          {product.brand}
        </div>

        {/* Name */}
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
          {truncateText(product.name, 50)}
        </h3>

        {/* Description */}
        <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
          {truncateText(product.description, 80)}
        </p>

        {/* Key Specifications */}
        {product.specifications && (
          <div className="space-y-1 mb-4 text-xs">
            {product.specifications.flowRate && (
              <div className="flex justify-between">
                <span className="text-slate-500">Caudal:</span>
                <span className="font-medium">
                  {product.specifications.flowRate.min}-{product.specifications.flowRate.max} {product.specifications.flowRate.unit}
                </span>
              </div>
            )}
            {product.specifications.pressure && (
              <div className="flex justify-between">
                <span className="text-slate-500">Presión:</span>
                <span className="font-medium">
                  {product.specifications.pressure.min}-{product.specifications.pressure.max} {product.specifications.pressure.unit}
                </span>
              </div>
            )}
            {product.specifications.power && (
              <div className="flex justify-between">
                <span className="text-slate-500">Potencia:</span>
                <span className="font-medium">
                  {product.specifications.power.value} {product.specifications.power.unit}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Price and Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
          <div>
            {product.price ? (
              <span className="text-xl font-bold text-green-600">
                {formatCurrency(product.price)}
              </span>
            ) : (
              <span className="text-lg font-medium text-slate-600 dark:text-slate-400">
                Consultar precio
              </span>
            )}
          </div>

          <Button
            onClick={handleWhatsAppClick}
            variant="primary"
            size="sm"
            leftIcon={<FaWhatsapp size={16} />}
            className="bg-green-600 hover:bg-green-700"
          >
            Consultar
          </Button>
        </div>
      </div>
    </motion.div>
  );
};
