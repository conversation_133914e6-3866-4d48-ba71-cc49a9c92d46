import React from 'react';
import { motion } from 'framer-motion';
import { Package } from 'lucide-react';
import { Spinner } from '@shared/ui';
import { ProductCard } from './ProductCard';
import { ProductGridProps } from '../types';

export const ProductGrid: React.FC<ProductGridProps> = ({ 
  products, 
  isLoading = false, 
  onProductSelect 
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-slate-600 dark:text-slate-400">
            Cargando productos...
          </p>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-20"
      >
        <Package className="mx-auto h-16 w-16 text-slate-400 mb-4" />
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
          No se encontraron productos
        </h3>
        <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto">
          No hay productos que coincidan con los filtros seleccionados. 
          Intenta ajustar los criterios de búsqueda.
        </p>
      </motion.div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.map((product, index) => (
        <motion.div
          key={product.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <ProductCard 
            product={product} 
            onSelect={onProductSelect}
          />
        </motion.div>
      ))}
    </div>
  );
};
