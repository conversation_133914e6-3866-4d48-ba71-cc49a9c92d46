import { ContactForm, SupportTicket, FAQ } from '../types';

// Mock FAQ data
const MOCK_FAQS: FAQ[] = [
  {
    id: '1',
    question: '¿Cómo selecciono la bomba correcta para mi aplicación?',
    answer: 'Para seleccionar la bomba correcta, necesitas considerar: 1) El caudal requerido (L/min o m³/h), 2) La altura total de elevación (mca), 3) El tipo de fluido a bombear, 4) Las condiciones de instalación. Puedes usar nuestro buscador avanzado o contactar a nuestros expertos.',
    category: 'products',
    tags: ['selección', 'bomba', 'caudal', 'altura'],
    helpful: 45,
    notHelpful: 3
  },
  {
    id: '2',
    question: '¿Qué mantenimiento requieren las bombas centrífugas?',
    answer: 'Las bombas centrífugas requieren: 1) Inspección visual regular, 2) Verificación de vibraciones y ruidos, 3) Control de fugas en sellos, 4) Lubricación de rodamientos según especificaciones, 5) Limpieza de filtros de succión. La frecuencia depende del uso y condiciones de operación.',
    category: 'maintenance',
    tags: ['mantenimiento', 'centrífuga', 'lubricación', 'inspección'],
    helpful: 38,
    notHelpful: 2
  },
  {
    id: '3',
    question: '¿Por qué mi bomba no arranca o no da presión?',
    answer: 'Las causas más comunes son: 1) Falta de cebado (bomba sin agua), 2) Obstrucción en succión o descarga, 3) Válvulas cerradas, 4) Problemas eléctricos, 5) Desgaste del impulsor. Verifica estos puntos antes de contactar soporte técnico.',
    category: 'troubleshooting',
    tags: ['problemas', 'presión', 'cebado', 'obstrucción'],
    helpful: 52,
    notHelpful: 5
  },
  {
    id: '4',
    question: '¿Ofrecen instalación y puesta en marcha?',
    answer: 'Sí, ofrecemos servicios de instalación y puesta en marcha para equipos de mayor complejidad. Nuestros técnicos certificados pueden realizar la instalación completa, pruebas de funcionamiento y capacitación del personal. Contacta ventas para más información.',
    category: 'installation',
    tags: ['instalación', 'puesta en marcha', 'técnicos', 'capacitación'],
    helpful: 29,
    notHelpful: 1
  },
  {
    id: '5',
    question: '¿Cuál es la garantía de los productos?',
    answer: 'La garantía varía según el fabricante y producto: KSB y Grundfos ofrecen 2-3 años, productos nacionales 1-2 años. Cubre defectos de fabricación, no desgaste normal o mal uso. La garantía incluye repuestos y mano de obra en nuestro taller autorizado.',
    category: 'general',
    tags: ['garantía', 'fabricante', 'repuestos', 'cobertura'],
    helpful: 41,
    notHelpful: 2
  }
];

/**
 * Submit contact form
 */
export const submitContactForm = async (formData: ContactForm): Promise<{ success: boolean; ticketId?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Simulate 95% success rate
  if (Math.random() > 0.05) {
    const ticketId = `TK-${Date.now()}`;
    
    // In a real app, this would send to backend
    console.log('Contact form submitted:', { ...formData, ticketId });
    
    return { success: true, ticketId };
  } else {
    throw new Error('Error al enviar el formulario. Intenta nuevamente.');
  }
};

/**
 * Get FAQs by category
 */
export const getFAQs = async (category?: string): Promise<FAQ[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  if (category) {
    return MOCK_FAQS.filter(faq => faq.category === category);
  }
  
  return MOCK_FAQS;
};

/**
 * Search FAQs
 */
export const searchFAQs = async (query: string): Promise<FAQ[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const searchTerm = query.toLowerCase();
  
  return MOCK_FAQS.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm) ||
    faq.answer.toLowerCase().includes(searchTerm) ||
    faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
};

/**
 * Rate FAQ helpfulness
 */
export const rateFAQ = async (faqId: string, helpful: boolean): Promise<void> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  // In a real app, this would update the backend
  console.log(`FAQ ${faqId} rated as ${helpful ? 'helpful' : 'not helpful'}`);
};

/**
 * Create support ticket
 */
export const createSupportTicket = async (ticketData: Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt'>): Promise<SupportTicket> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const ticket: SupportTicket = {
    ...ticketData,
    id: `TK-${Date.now()}`,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  return ticket;
};
