import { formatPhoneForWhatsApp } from '@shared/utils';
import { COMPANY_INFO } from '@shared/constants';
import { WhatsAppMessage } from '../types';

/**
 * Generate WhatsApp URL with pre-filled message
 */
export const generateWhatsAppURL = (message: WhatsAppMessage): string => {
  const phoneNumber = formatPhoneForWhatsApp(COMPANY_INFO.phone);
  let formattedMessage = '';

  switch (message.type) {
    case 'general':
      formattedMessage = `¡Hola! ${message.message}`;
      break;

    case 'product-inquiry':
      formattedMessage = `🔍 CONSULTA DE PRODUCTO

${message.message}

${message.customerInfo?.name ? `👤 Nombre: ${message.customerInfo.name}` : ''}
${message.customerInfo?.company ? `🏢 Empresa: ${message.customerInfo.company}` : ''}
${message.customerInfo?.location ? `📍 Ubicación: ${message.customerInfo.location}` : ''}

¿<PERSON>ueden ayudarme con más información?`;
      break;

    case 'technical-support':
      formattedMessage = `🔧 SOPORTE TÉCNICO

${message.message}

${message.customerInfo?.name ? `👤 Nombre: ${message.customerInfo.name}` : ''}
${message.customerInfo?.company ? `🏢 Empresa: ${message.customerInfo.company}` : ''}

Necesito asistencia técnica urgente.`;
      break;

    case 'quote-request':
      formattedMessage = `💰 SOLICITUD DE COTIZACIÓN

${message.message}

${message.customerInfo?.name ? `👤 Nombre: ${message.customerInfo.name}` : ''}
${message.customerInfo?.company ? `🏢 Empresa: ${message.customerInfo.company}` : ''}
${message.customerInfo?.location ? `📍 Ubicación: ${message.customerInfo.location}` : ''}

¿Pueden enviarme una cotización detallada?`;
      break;

    default:
      formattedMessage = message.message;
  }

  return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(formattedMessage.trim())}`;
};

/**
 * Open WhatsApp with message
 */
export const openWhatsApp = (message: WhatsAppMessage): void => {
  const url = generateWhatsAppURL(message);
  window.open(url, '_blank');
};

/**
 * Quick WhatsApp messages for common scenarios
 */
export const quickWhatsAppMessages = {
  generalInquiry: (customerName?: string): WhatsAppMessage => ({
    type: 'general',
    message: `Hola! ${customerName ? `Soy ${customerName} y ` : ''}necesito información sobre sus productos y servicios de riego. ¿Podrían asesorarme?`,
    customerInfo: customerName ? { name: customerName } : undefined
  }),

  productInquiry: (productName: string, customerInfo?: WhatsAppMessage['customerInfo']): WhatsAppMessage => ({
    type: 'product-inquiry',
    message: `Me interesa el producto: ${productName}. ¿Pueden darme más información sobre especificaciones, precio y disponibilidad?`,
    customerInfo
  }),

  technicalSupport: (issue: string, customerInfo?: WhatsAppMessage['customerInfo']): WhatsAppMessage => ({
    type: 'technical-support',
    message: `Tengo un problema técnico: ${issue}`,
    customerInfo
  }),

  quoteRequest: (requirements: string, customerInfo?: WhatsAppMessage['customerInfo']): WhatsAppMessage => ({
    type: 'quote-request',
    message: `Necesito cotización para: ${requirements}`,
    customerInfo
  })
};
