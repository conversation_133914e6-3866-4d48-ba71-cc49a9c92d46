export interface ContactForm {
  name: string;
  email: string;
  phone: string;
  company?: string;
  subject: string;
  message: string;
  urgency: 'low' | 'medium' | 'high';
  preferredContact: 'email' | 'phone' | 'whatsapp';
}

export interface WhatsAppMessage {
  type: 'general' | 'product-inquiry' | 'technical-support' | 'quote-request';
  message: string;
  productId?: string;
  customerInfo?: {
    name?: string;
    company?: string;
    location?: string;
  };
}

export interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'sales' | 'billing' | 'general';
  createdAt: Date;
  updatedAt: Date;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
    company?: string;
  };
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'installation' | 'maintenance' | 'troubleshooting' | 'products' | 'general';
  tags: string[];
  helpful: number;
  notHelpful: number;
}

export interface ContactInfo {
  phone: string;
  email: string;
  whatsapp: string;
  address: string;
  hours: {
    weekdays: string;
    saturday: string;
    sunday: string;
  };
  socialMedia: {
    facebook?: string;
    instagram?: string;
    linkedin?: string;
  };
}

export interface LiveChatState {
  isOpen: boolean;
  isConnected: boolean;
  messages: ChatMessage[];
  isTyping: boolean;
}

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  type: 'text' | 'image' | 'file';
}
