import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, ChevronDown, ChevronUp, ThumbsUp, ThumbsDown, HelpCircle } from 'lucide-react';
import { Input, But<PERSON>, Spinner } from '@shared/ui';
import { useFAQ } from '../hooks';

const FAQ_CATEGORIES = [
  { id: 'all', label: 'Todas', icon: '📋' },
  { id: 'products', label: 'Productos', icon: '🔧' },
  { id: 'installation', label: 'Instalación', icon: '⚙️' },
  { id: 'maintenance', label: 'Mantenimiento', icon: '🔨' },
  { id: 'troubleshooting', label: 'Problemas', icon: '🚨' },
  { id: 'general', label: 'General', icon: '❓' }
];

export const FAQSection: React.FC = () => {
  const {
    faqs,
    isLoading,
    error,
    searchQuery,
    selectedCategory,
    setSearchQuery,
    setSelectedCategory,
    rateFAQ
  } = useFAQ();

  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [ratedFAQs, setRatedFAQs] = useState<Set<string>>(new Set());

  const handleCategoryChange = (categoryId: string) => {
    const category = categoryId === 'all' ? null : categoryId;
    setSelectedCategory(category);
  };

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const handleRating = async (faqId: string, helpful: boolean) => {
    if (ratedFAQs.has(faqId)) return;
    
    await rateFAQ(faqId, helpful);
    setRatedFAQs(prev => new Set([...prev, faqId]));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <HelpCircle className="mx-auto h-12 w-12 text-orange-500 mb-4" />
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
          Preguntas Frecuentes
        </h3>
        <p className="text-slate-600 dark:text-slate-400">
          Encuentra respuestas rápidas a las consultas más comunes
        </p>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
          <Input
            type="text"
            placeholder="Buscar en preguntas frecuentes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex flex-wrap gap-2 mb-8">
        {FAQ_CATEGORIES.map((category) => (
          <Button
            key={category.id}
            onClick={() => handleCategoryChange(category.id)}
            variant={
              (category.id === 'all' && !selectedCategory) || 
              selectedCategory === category.id 
                ? 'primary' 
                : 'outline'
            }
            size="sm"
            className="text-sm"
          >
            <span className="mr-1">{category.icon}</span>
            {category.label}
          </Button>
        ))}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <Spinner size="lg" />
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-12">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* FAQ List */}
      {!isLoading && !error && (
        <div className="space-y-4">
          {faqs.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-slate-600 dark:text-slate-400">
                No se encontraron preguntas frecuentes para tu búsqueda.
              </p>
            </div>
          ) : (
            faqs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden"
              >
                {/* Question */}
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full px-6 py-4 text-left bg-slate-50 dark:bg-slate-700 hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors flex items-center justify-between"
                >
                  <span className="font-medium text-slate-900 dark:text-white pr-4">
                    {faq.question}
                  </span>
                  {expandedFAQ === faq.id ? (
                    <ChevronUp className="h-5 w-5 text-slate-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-slate-500 flex-shrink-0" />
                  )}
                </button>

                {/* Answer */}
                <AnimatePresence>
                  {expandedFAQ === faq.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 py-4 bg-white dark:bg-slate-800">
                        <p className="text-slate-700 dark:text-slate-300 mb-4 leading-relaxed">
                          {faq.answer}
                        </p>

                        {/* Tags */}
                        {faq.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-4">
                            {faq.tags.map((tag) => (
                              <span
                                key={tag}
                                className="px-2 py-1 bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 text-xs rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Rating */}
                        <div className="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            ¿Te fue útil esta respuesta?
                          </span>
                          
                          <div className="flex items-center space-x-2">
                            <Button
                              onClick={() => handleRating(faq.id, true)}
                              disabled={ratedFAQs.has(faq.id)}
                              variant="ghost"
                              size="sm"
                              leftIcon={<ThumbsUp size={16} />}
                              className="text-green-600 hover:text-green-700"
                            >
                              {faq.helpful}
                            </Button>
                            
                            <Button
                              onClick={() => handleRating(faq.id, false)}
                              disabled={ratedFAQs.has(faq.id)}
                              variant="ghost"
                              size="sm"
                              leftIcon={<ThumbsDown size={16} />}
                              className="text-red-600 hover:text-red-700"
                            >
                              {faq.notHelpful}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))
          )}
        </div>
      )}
    </motion.div>
  );
};
