import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaWhatsapp } from 'react-icons/fa';
import { X, MessageCircle, User, Building, MapPin } from 'lucide-react';
import { Button, Input } from '@shared/ui';
import { openWhatsApp, quickWhatsAppMessages } from '../services';
import { WhatsAppMessage } from '../types';

interface WhatsAppButtonProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showQuickActions?: boolean;
}

export const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({
  className = '',
  size = 'lg',
  showQuickActions = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    company: '',
    location: ''
  });

  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-14 h-14',
    lg: 'w-16 h-16'
  };

  const iconSizes = {
    sm: 20,
    md: 24,
    lg: 28
  };

  const handleQuickMessage = (messageType: keyof typeof quickWhatsAppMessages) => {
    let message: WhatsAppMessage;

    switch (messageType) {
      case 'generalInquiry':
        message = quickWhatsAppMessages.generalInquiry(customerInfo.name || undefined);
        break;
      case 'productInquiry':
        message = quickWhatsAppMessages.productInquiry(
          'información general de productos',
          customerInfo.name ? customerInfo : undefined
        );
        break;
      case 'technicalSupport':
        message = quickWhatsAppMessages.technicalSupport(
          'necesito asistencia técnica',
          customerInfo.name ? customerInfo : undefined
        );
        break;
      case 'quoteRequest':
        message = quickWhatsAppMessages.quoteRequest(
          'sistema de riego completo',
          customerInfo.name ? customerInfo : undefined
        );
        break;
      default:
        return;
    }

    openWhatsApp(message);
    setIsExpanded(false);
  };

  const handleDirectMessage = () => {
    const message = quickWhatsAppMessages.generalInquiry(customerInfo.name || undefined);
    openWhatsApp(message);
    setIsExpanded(false);
  };

  return (
    <>
      {/* Main WhatsApp Button */}
      <motion.button
        onClick={() => showQuickActions ? setIsExpanded(true) : handleDirectMessage()}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        className={`
          fixed bottom-6 right-6 z-50 
          ${sizeClasses[size]} 
          bg-green-500 hover:bg-green-600 
          text-white rounded-full shadow-lg hover:shadow-xl 
          transition-all duration-300 
          flex items-center justify-center
          ${className}
        `}
      >
        <FaWhatsapp size={iconSizes[size]} />
      </motion.button>

      {/* Expanded Panel */}
      <AnimatePresence>
        {isExpanded && showQuickActions && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsExpanded(false)}
              className="fixed inset-0 bg-black/50 z-40"
            />

            {/* Panel */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fixed bottom-24 right-6 z-50 w-80 bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-6"
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <FaWhatsapp className="text-green-500" size={24} />
                  <h3 className="font-semibold text-slate-900 dark:text-white">
                    Contactar por WhatsApp
                  </h3>
                </div>
                <button
                  onClick={() => setIsExpanded(false)}
                  className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Customer Info Form */}
              <div className="space-y-3 mb-4">
                <Input
                  placeholder="Tu nombre (opcional)"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                  leftIcon={<User size={16} />}
                  size="sm"
                />
                <Input
                  placeholder="Empresa (opcional)"
                  value={customerInfo.company}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, company: e.target.value }))}
                  leftIcon={<Building size={16} />}
                  size="sm"
                />
                <Input
                  placeholder="Ubicación (opcional)"
                  value={customerInfo.location}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, location: e.target.value }))}
                  leftIcon={<MapPin size={16} />}
                  size="sm"
                />
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <Button
                  onClick={() => handleQuickMessage('generalInquiry')}
                  variant="outline"
                  size="sm"
                  leftIcon={<MessageCircle size={16} />}
                  className="w-full justify-start"
                >
                  Consulta general
                </Button>
                
                <Button
                  onClick={() => handleQuickMessage('productInquiry')}
                  variant="outline"
                  size="sm"
                  leftIcon={<MessageCircle size={16} />}
                  className="w-full justify-start"
                >
                  Consultar productos
                </Button>
                
                <Button
                  onClick={() => handleQuickMessage('technicalSupport')}
                  variant="outline"
                  size="sm"
                  leftIcon={<MessageCircle size={16} />}
                  className="w-full justify-start"
                >
                  Soporte técnico
                </Button>
                
                <Button
                  onClick={() => handleQuickMessage('quoteRequest')}
                  variant="outline"
                  size="sm"
                  leftIcon={<MessageCircle size={16} />}
                  className="w-full justify-start"
                >
                  Solicitar cotización
                </Button>
              </div>

              {/* Direct Contact */}
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                <Button
                  onClick={handleDirectMessage}
                  className="w-full bg-green-500 hover:bg-green-600"
                  leftIcon={<FaWhatsapp size={16} />}
                >
                  Abrir WhatsApp
                </Button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
