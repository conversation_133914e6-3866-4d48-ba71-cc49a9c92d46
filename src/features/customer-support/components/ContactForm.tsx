import React from 'react';
import { motion } from 'framer-motion';
import { Send, CheckCircle, AlertCircle } from 'lucide-react';
import { Button, Input } from '@shared/ui';
import { useContactForm } from '../hooks';

export const ContactForm: React.FC = () => {
  const {
    formData,
    isSubmitting,
    isSubmitted,
    error,
    ticketId,
    updateField,
    submitForm,
    resetForm,
    isFormValid
  } = useContactForm();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await submitForm();
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl text-center"
      >
        <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
          ¡Mensaje enviado exitosamente!
        </h3>
        <p className="text-slate-600 dark:text-slate-400 mb-4">
          Hemos recibido tu consulta y te responderemos a la brevedad.
        </p>
        {ticketId && (
          <p className="text-sm text-slate-500 dark:text-slate-400 mb-6">
            Número de ticket: <span className="font-mono font-medium">{ticketId}</span>
          </p>
        )}
        <Button onClick={resetForm} variant="outline">
          Enviar otro mensaje
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl"
    >
      <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
        Contáctanos
      </h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Nombre completo *"
            type="text"
            value={formData.name}
            onChange={(e) => updateField('name', e.target.value)}
            placeholder="Tu nombre completo"
            required
          />
          
          <Input
            label="Email *"
            type="email"
            value={formData.email}
            onChange={(e) => updateField('email', e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Teléfono *"
            type="tel"
            value={formData.phone}
            onChange={(e) => updateField('phone', e.target.value)}
            placeholder="+56 9 1234 5678"
            required
          />
          
          <Input
            label="Empresa (opcional)"
            type="text"
            value={formData.company}
            onChange={(e) => updateField('company', e.target.value)}
            placeholder="Nombre de tu empresa"
          />
        </div>

        {/* Subject */}
        <Input
          label="Asunto *"
          type="text"
          value={formData.subject}
          onChange={(e) => updateField('subject', e.target.value)}
          placeholder="¿En qué podemos ayudarte?"
          required
        />

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Mensaje *
          </label>
          <textarea
            value={formData.message}
            onChange={(e) => updateField('message', e.target.value)}
            placeholder="Describe tu consulta o requerimiento..."
            rows={5}
            className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-vertical"
            required
          />
        </div>

        {/* Urgency and Contact Preference */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Urgencia
            </label>
            <select
              value={formData.urgency}
              onChange={(e) => updateField('urgency', e.target.value)}
              className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="low">Baja</option>
              <option value="medium">Media</option>
              <option value="high">Alta</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Contacto preferido
            </label>
            <select
              value={formData.preferredContact}
              onChange={(e) => updateField('preferredContact', e.target.value)}
              className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="email">Email</option>
              <option value="phone">Teléfono</option>
              <option value="whatsapp">WhatsApp</option>
            </select>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
          >
            <AlertCircle className="h-5 w-5 text-red-500" />
            <span className="text-red-700 dark:text-red-400 text-sm">{error}</span>
          </motion.div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={!isFormValid || isSubmitting}
          isLoading={isSubmitting}
          leftIcon={<Send size={20} />}
          className="w-full"
        >
          {isSubmitting ? 'Enviando...' : 'Enviar mensaje'}
        </Button>
      </form>
    </motion.div>
  );
};
