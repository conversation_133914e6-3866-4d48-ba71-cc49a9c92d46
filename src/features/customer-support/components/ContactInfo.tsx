import { COMPANY_INFO } from '@shared/constants';
import { Button } from '@shared/ui';
import { motion } from 'framer-motion';
import { Clock, Mail, MapPin, Phone } from 'lucide-react';
import React from 'react';
import { FaFacebook, FaInstagram, FaLinkedin, FaWhatsapp } from 'react-icons/fa';
import { openWhatsApp, quickWhatsAppMessages } from '../services';

export const ContactInfo: React.FC = () => {
  const handleWhatsAppClick = () => {
    const message = quickWhatsAppMessages.generalInquiry();
    openWhatsApp(message);
  };

  const handlePhoneClick = () => {
    window.open(`tel:${COMPANY_INFO.phone}`, '_self');
  };

  const handleEmailClick = () => {
    window.open(`mailto:${COMPANY_INFO.email}`, '_self');
  };

  const contactItems = [
    {
      icon: Phone,
      label: 'Teléfono',
      value: COMPANY_INFO.phone,
      action: handlePhoneClick,
      color: 'text-blue-600'
    },
    {
      icon: Mail,
      label: 'Email',
      value: COMPANY_INFO.email,
      action: handleEmailClick,
      color: 'text-green-600'
    },
    {
      icon: MapPin,
      label: 'Dirección',
      value: COMPANY_INFO.address,
      action: () => {
        // Open in Google Maps
        const encodedAddress = encodeURIComponent(COMPANY_INFO.address);
        window.open(`https://maps.google.com/?q=${encodedAddress}`, '_blank');
      },
      color: 'text-red-600'
    }
  ];

  const businessHours = [
    { day: 'Lunes - Viernes', hours: '8:00 - 18:00' },
    { day: 'Sábados', hours: '9:00 - 13:00' },
    { day: 'Domingos', hours: 'Cerrado' }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
          Información de Contacto
        </h3>
        <p className="text-slate-600 dark:text-slate-400">
          Múltiples formas de comunicarte con nosotros
        </p>
      </div>

      {/* Contact Methods */}
      <div className="space-y-6 mb-8">
        {contactItems.map((item, index) => (
          <motion.div
            key={item.label}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            onClick={item.action}
            className="flex items-center space-x-4 p-4 rounded-lg bg-slate-50 dark:bg-slate-700 hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors cursor-pointer group"
          >
            <div className={`p-3 rounded-full bg-white dark:bg-slate-800 ${item.color} group-hover:scale-110 transition-transform`}>
              <item.icon size={20} />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-slate-900 dark:text-white">
                {item.label}
              </h4>
              <p className="text-slate-600 dark:text-slate-400 text-sm">
                {item.value}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* WhatsApp CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mb-8"
      >
        <Button
          onClick={handleWhatsAppClick}
          className="w-full bg-green-500 hover:bg-green-600 text-white"
          leftIcon={<FaWhatsapp size={20} />}
          size="lg"
        >
          Contactar por WhatsApp
        </Button>
      </motion.div>

      {/* Business Hours */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-2 mb-4">
          <Clock className="text-orange-500" size={20} />
          <h4 className="font-semibold text-slate-900 dark:text-white">
            Horarios de Atención
          </h4>
        </div>
        
        <div className="space-y-2">
          {businessHours.map((schedule, index) => (
            <div key={index} className="flex justify-between items-center py-2 border-b border-slate-200 dark:border-slate-700 last:border-b-0">
              <span className="text-slate-600 dark:text-slate-400">
                {schedule.day}
              </span>
              <span className="font-medium text-slate-900 dark:text-white">
                {schedule.hours}
              </span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Social Media */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <h4 className="font-semibold text-slate-900 dark:text-white mb-4 text-center">
          Síguenos en redes sociales
        </h4>
        
        <div className="flex justify-center space-x-4">
          <a
            href="#"
            className="p-3 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors"
            aria-label="Facebook"
          >
            <FaFacebook size={20} />
          </a>
          
          <a
            href="#"
            className="p-3 rounded-full bg-pink-600 text-white hover:bg-pink-700 transition-colors"
            aria-label="Instagram"
          >
            <FaInstagram size={20} />
          </a>
          
          <a
            href="#"
            className="p-3 rounded-full bg-blue-800 text-white hover:bg-blue-900 transition-colors"
            aria-label="LinkedIn"
          >
            <FaLinkedin size={20} />
          </a>
        </div>
      </motion.div>
    </motion.div>
  );
};
