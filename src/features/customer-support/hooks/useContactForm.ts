import { useState, useCallback } from 'react';
import { ContactForm } from '../types';
import { submitContactForm } from '../services';

interface ContactFormState {
  isSubmitting: boolean;
  isSubmitted: boolean;
  error: string | null;
  ticketId: string | null;
}

const initialFormData: ContactForm = {
  name: '',
  email: '',
  phone: '',
  company: '',
  subject: '',
  message: '',
  urgency: 'medium',
  preferredContact: 'email'
};

export const useContactForm = () => {
  const [formData, setFormData] = useState<ContactForm>(initialFormData);
  const [state, setState] = useState<ContactFormState>({
    isSubmitting: false,
    isSubmitted: false,
    error: null,
    ticketId: null
  });

  const updateField = useCallback((field: keyof ContactForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const updateFormData = useCallback((data: Partial<ContactForm>) => {
    setFormData(prev => ({ ...prev, ...data }));
  }, []);

  const validateForm = useCallback((): string[] => {
    const errors: string[] = [];

    if (!formData.name.trim()) {
      errors.push('El nombre es requerido');
    }

    if (!formData.email.trim()) {
      errors.push('El email es requerido');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('El email no es válido');
    }

    if (!formData.phone.trim()) {
      errors.push('El teléfono es requerido');
    }

    if (!formData.subject.trim()) {
      errors.push('El asunto es requerido');
    }

    if (!formData.message.trim()) {
      errors.push('El mensaje es requerido');
    } else if (formData.message.length < 10) {
      errors.push('El mensaje debe tener al menos 10 caracteres');
    }

    return errors;
  }, [formData]);

  const submitForm = useCallback(async (): Promise<boolean> => {
    const validationErrors = validateForm();
    
    if (validationErrors.length > 0) {
      setState(prev => ({
        ...prev,
        error: validationErrors.join(', ')
      }));
      return false;
    }

    setState(prev => ({
      ...prev,
      isSubmitting: true,
      error: null
    }));

    try {
      const result = await submitContactForm(formData);
      
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        isSubmitted: true,
        ticketId: result.ticketId || null
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        error: error instanceof Error ? error.message : 'Error al enviar el formulario'
      }));

      return false;
    }
  }, [formData, validateForm]);

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setState({
      isSubmitting: false,
      isSubmitted: false,
      error: null,
      ticketId: null
    });
  }, []);

  const isFormValid = useCallback(() => {
    return validateForm().length === 0;
  }, [validateForm]);

  return {
    formData,
    ...state,
    updateField,
    updateFormData,
    submitForm,
    resetForm,
    isFormValid: isFormValid(),
    validationErrors: validateForm()
  };
};
