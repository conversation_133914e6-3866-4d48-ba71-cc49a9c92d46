import { useState, useCallback, useEffect } from 'react';
import { FAQ } from '../types';
import { getFAQs, searchFAQs, rateFAQ } from '../services';

interface FAQState {
  faqs: FAQ[];
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  selectedCategory: string | null;
}

export const useFAQ = () => {
  const [state, setState] = useState<FAQState>({
    faqs: [],
    isLoading: false,
    error: null,
    searchQuery: '',
    selectedCategory: null
  });

  const loadFAQs = useCallback(async (category?: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const faqs = await getFAQs(category);
      setState(prev => ({
        ...prev,
        faqs,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Error al cargar las preguntas frecuentes',
        isLoading: false
      }));
    }
  }, []);

  const searchFAQsHandler = useCallback(async (query: string) => {
    if (!query.trim()) {
      loadFAQs(state.selectedCategory || undefined);
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const faqs = await searchFAQs(query);
      setState(prev => ({
        ...prev,
        faqs,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Error al buscar preguntas frecuentes',
        isLoading: false
      }));
    }
  }, [state.selectedCategory, loadFAQs]);

  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      searchFAQsHandler(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchFAQsHandler]);

  const setSelectedCategory = useCallback((category: string | null) => {
    setState(prev => ({ 
      ...prev, 
      selectedCategory: category,
      searchQuery: '' // Clear search when changing category
    }));
    loadFAQs(category || undefined);
  }, [loadFAQs]);

  const rateFAQHandler = useCallback(async (faqId: string, helpful: boolean) => {
    try {
      await rateFAQ(faqId, helpful);
      
      // Update local state
      setState(prev => ({
        ...prev,
        faqs: prev.faqs.map(faq => 
          faq.id === faqId 
            ? {
                ...faq,
                helpful: helpful ? faq.helpful + 1 : faq.helpful,
                notHelpful: !helpful ? faq.notHelpful + 1 : faq.notHelpful
              }
            : faq
        )
      }));
    } catch (error) {
      console.error('Error rating FAQ:', error);
    }
  }, []);

  // Load initial FAQs
  useEffect(() => {
    loadFAQs();
  }, [loadFAQs]);

  return {
    ...state,
    setSearchQuery,
    setSelectedCategory,
    rateFAQ: rateFAQHandler,
    reload: () => loadFAQs(state.selectedCategory || undefined)
  };
};
