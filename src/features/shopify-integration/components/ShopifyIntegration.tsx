import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, ExternalLink } from 'lucide-react';
import { Button } from '@shared/ui';

export const ShopifyIntegration: React.FC = () => {
  const handleShopifyRedirect = () => {
    window.open('https://hidroimplementos.myshopify.com', '_blank');
  };

  return (
    <section className="py-20 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <ShoppingCart className="mx-auto h-16 w-16 text-orange-500 mb-6" />
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Tienda Online
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto mb-8">
            Visita nuestra tienda online para explorar nuestro catálogo completo 
            y realizar compras de manera segura.
          </p>
          
          <Button
            onClick={handleShopifyRedirect}
            size="lg"
            leftIcon={<ExternalLink size={20} />}
          >
            Ir a la tienda
          </Button>
        </motion.div>
      </div>
    </section>
  );
};
