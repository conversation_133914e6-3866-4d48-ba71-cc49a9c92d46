import { useState, useCallback } from 'react';
import { ShopifyProduct, ShopifyCart } from '../types';
import { 
  fetchShopifyProducts, 
  createShopifyCart, 
  addToShopifyCart 
} from '../services';

interface ShopifyState {
  products: ShopifyProduct[];
  cart: ShopifyCart | null;
  isLoading: boolean;
  error: string | null;
}

export const useShopify = () => {
  const [state, setState] = useState<ShopifyState>({
    products: [],
    cart: null,
    isLoading: false,
    error: null
  });

  const loadProducts = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await fetchShopifyProducts();
      setState(prev => ({
        ...prev,
        products: result.products,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Error al cargar productos de Shopify',
        isLoading: false
      }));
    }
  }, []);

  const initializeCart = useCallback(async () => {
    try {
      const cart = await createShopifyCart();
      setState(prev => ({ ...prev, cart }));
      return cart;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Error al inicializar carrito'
      }));
      return null;
    }
  }, []);

  const addToCart = useCallback(async (variantId: string, quantity: number = 1) => {
    if (!state.cart) {
      await initializeCart();
    }

    if (!state.cart) return false;

    try {
      const updatedCart = await addToShopifyCart(state.cart.id, variantId, quantity);
      setState(prev => ({ ...prev, cart: updatedCart }));
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Error al agregar producto al carrito'
      }));
      return false;
    }
  }, [state.cart, initializeCart]);

  return {
    ...state,
    loadProducts,
    initializeCart,
    addToCart
  };
};
