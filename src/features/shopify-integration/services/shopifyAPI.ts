import { ShopifyProduct, ShopifyCollection, ShopifyCart } from '../types';

// Mock Shopify configuration
const SHOPIFY_CONFIG = {
  domain: 'hidroimplementos.myshopify.com',
  storefrontAccessToken: 'your-storefront-access-token',
  apiVersion: '2023-10'
};

/**
 * Mock Shopify API service
 * In a real implementation, this would connect to Shopify's Storefront API
 */

/**
 * Fetch products from Shopify
 */
export const fetchShopifyProducts = async (
  limit: number = 20,
  cursor?: string
): Promise<{ products: ShopifyProduct[]; hasNextPage: boolean; endCursor?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock products data
  const mockProducts: ShopifyProduct[] = [
    {
      id: 'gid://shopify/Product/1',
      title: 'Bomba Centrífuga KSB Etanorm 50-32-200',
      description: 'Bomba centrífuga de alta eficiencia para aplicaciones industriales',
      price: 850000,
      compareAtPrice: 950000,
      images: [
        {
          id: 'img1',
          url: '/products/ksb-etanorm.jpg',
          altText: 'Bomba KSB Etanorm',
          width: 800,
          height: 600
        }
      ],
      variants: [
        {
          id: 'var1',
          title: 'Default',
          price: 850000,
          availableForSale: true,
          quantityAvailable: 5,
          selectedOptions: []
        }
      ],
      tags: ['bomba', 'centrífuga', 'ksb', 'industrial'],
      vendor: 'KSB',
      productType: 'Bomba',
      handle: 'bomba-centrifuga-ksb-etanorm-50-32-200',
      availableForSale: true,
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-12-01T00:00:00Z'
    }
  ];

  return {
    products: mockProducts,
    hasNextPage: false,
    endCursor: undefined
  };
};

/**
 * Fetch a single product by handle
 */
export const fetchShopifyProduct = async (handle: string): Promise<ShopifyProduct | null> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock single product fetch
  const products = await fetchShopifyProducts();
  return products.products.find(p => p.handle === handle) || null;
};

/**
 * Fetch collections
 */
export const fetchShopifyCollections = async (): Promise<ShopifyCollection[]> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  
  return [
    {
      id: 'col1',
      title: 'Bombas Centrífugas',
      description: 'Bombas centrífugas para diversas aplicaciones',
      handle: 'bombas-centrifugas',
      products: []
    },
    {
      id: 'col2',
      title: 'Sistemas de Riego',
      description: 'Sistemas completos de riego automatizado',
      handle: 'sistemas-riego',
      products: []
    }
  ];
};

/**
 * Create cart
 */
export const createShopifyCart = async (): Promise<ShopifyCart> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return {
    id: 'cart-' + Date.now(),
    lines: [],
    totalQuantity: 0,
    cost: {
      totalAmount: 0,
      subtotalAmount: 0,
      totalTaxAmount: 0
    },
    checkoutUrl: 'https://hidroimplementos.myshopify.com/cart'
  };
};

/**
 * Add to cart
 */
export const addToShopifyCart = async (
  cartId: string,
  variantId: string,
  quantity: number
): Promise<ShopifyCart> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock cart update
  console.log(`Added ${quantity} of variant ${variantId} to cart ${cartId}`);
  
  return createShopifyCart(); // Return updated cart
};

/**
 * Update cart line
 */
export const updateShopifyCartLine = async (
  cartId: string,
  lineId: string,
  quantity: number
): Promise<ShopifyCart> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  
  console.log(`Updated line ${lineId} in cart ${cartId} to quantity ${quantity}`);
  
  return createShopifyCart(); // Return updated cart
};

/**
 * Remove from cart
 */
export const removeFromShopifyCart = async (
  cartId: string,
  lineId: string
): Promise<ShopifyCart> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  
  console.log(`Removed line ${lineId} from cart ${cartId}`);
  
  return createShopifyCart(); // Return updated cart
};
