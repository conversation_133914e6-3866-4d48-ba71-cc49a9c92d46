export interface ShopifyProduct {
  id: string;
  title: string;
  description: string;
  price: number;
  compareAtPrice?: number;
  images: ShopifyImage[];
  variants: ShopifyVariant[];
  tags: string[];
  vendor: string;
  productType: string;
  handle: string;
  availableForSale: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ShopifyImage {
  id: string;
  url: string;
  altText?: string;
  width: number;
  height: number;
}

export interface ShopifyVariant {
  id: string;
  title: string;
  price: number;
  compareAtPrice?: number;
  availableForSale: boolean;
  quantityAvailable: number;
  selectedOptions: ShopifySelectedOption[];
  image?: ShopifyImage;
}

export interface ShopifySelectedOption {
  name: string;
  value: string;
}

export interface ShopifyCollection {
  id: string;
  title: string;
  description: string;
  handle: string;
  image?: ShopifyImage;
  products: ShopifyProduct[];
}

export interface ShopifyCart {
  id: string;
  lines: ShopifyCartLine[];
  totalQuantity: number;
  cost: {
    totalAmount: number;
    subtotalAmount: number;
    totalTaxAmount: number;
  };
  checkoutUrl: string;
}

export interface ShopifyCartLine {
  id: string;
  quantity: number;
  merchandise: ShopifyVariant;
  cost: {
    totalAmount: number;
  };
}

export interface ShopifyCustomer {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  acceptsMarketing: boolean;
  addresses: ShopifyAddress[];
  orders: ShopifyOrder[];
}

export interface ShopifyAddress {
  id: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone?: string;
}

export interface ShopifyOrder {
  id: string;
  orderNumber: string;
  totalPrice: number;
  subtotalPrice: number;
  totalTax: number;
  createdAt: string;
  fulfillmentStatus: string;
  financialStatus: string;
  lineItems: ShopifyOrderLineItem[];
  shippingAddress?: ShopifyAddress;
  billingAddress?: ShopifyAddress;
}

export interface ShopifyOrderLineItem {
  id: string;
  title: string;
  quantity: number;
  price: number;
  variant: ShopifyVariant;
}
