export interface CompanyInfo {
  name: string;
  description: string;
  mission: string;
  vision: string;
  values: string[];
  foundedYear: number;
  employees: string;
  location: string;
  certifications: string[];
  awards: Award[];
}

export interface Award {
  id: string;
  title: string;
  year: number;
  organization: string;
  description: string;
  image?: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  department: string;
  bio: string;
  image: string;
  email?: string;
  linkedin?: string;
  specialties: string[];
  experience: string;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  benefits: string[];
  image?: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  client: string;
  location: string;
  completedDate: Date;
  category: 'residential' | 'commercial' | 'industrial' | 'agricultural';
  images: string[];
  technologies: string[];
  results: ProjectResult[];
}

export interface ProjectResult {
  metric: string;
  value: string;
  description: string;
}

export interface Testimonial {
  id: string;
  clientName: string;
  clientCompany: string;
  clientPosition: string;
  content: string;
  rating: number;
  date: Date;
  projectId?: string;
  image?: string;
}

export interface CompanyStats {
  projectsCompleted: number;
  clientsSatisfied: number;
  yearsExperience: number;
  teamMembers: number;
  certifications: number;
}

export interface Timeline {
  year: number;
  title: string;
  description: string;
  milestone: boolean;
}

export interface Partnership {
  id: string;
  name: string;
  logo: string;
  description: string;
  website?: string;
  type: 'manufacturer' | 'distributor' | 'technology' | 'service';
}
