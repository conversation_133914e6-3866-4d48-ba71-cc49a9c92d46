import { useState, useEffect, useCallback } from 'react';
import { 
  CompanyInfo, 
  TeamMember, 
  Service, 
  CompanyStats, 
  Timeline 
} from '../types';
import { 
  getCompanyInfo, 
  getTeamMembers, 
  getServices, 
  getCompanyStats, 
  getTimeline 
} from '../services';

interface CompanyProfileState {
  companyInfo: CompanyInfo | null;
  teamMembers: TeamMember[];
  services: Service[];
  stats: CompanyStats | null;
  timeline: Timeline[];
  isLoading: boolean;
  error: string | null;
}

export const useCompanyProfile = () => {
  const [state, setState] = useState<CompanyProfileState>({
    companyInfo: null,
    teamMembers: [],
    services: [],
    stats: null,
    timeline: [],
    isLoading: false,
    error: null
  });

  const loadCompanyInfo = useCallback(async () => {
    try {
      const info = await getCompanyInfo();
      setState(prev => ({ ...prev, companyInfo: info }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar información de la empresa' 
      }));
    }
  }, []);

  const loadTeamMembers = useCallback(async () => {
    try {
      const members = await getTeamMembers();
      setState(prev => ({ ...prev, teamMembers: members }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar el equipo' 
      }));
    }
  }, []);

  const loadServices = useCallback(async () => {
    try {
      const services = await getServices();
      setState(prev => ({ ...prev, services }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar los servicios' 
      }));
    }
  }, []);

  const loadStats = useCallback(async () => {
    try {
      const stats = await getCompanyStats();
      setState(prev => ({ ...prev, stats }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar las estadísticas' 
      }));
    }
  }, []);

  const loadTimeline = useCallback(async () => {
    try {
      const timeline = await getTimeline();
      setState(prev => ({ ...prev, timeline }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar la cronología' 
      }));
    }
  }, []);

  const loadAllData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await Promise.all([
        loadCompanyInfo(),
        loadTeamMembers(),
        loadServices(),
        loadStats(),
        loadTimeline()
      ]);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Error al cargar los datos de la empresa' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [loadCompanyInfo, loadTeamMembers, loadServices, loadStats, loadTimeline]);

  // Load data on mount
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  return {
    ...state,
    reload: loadAllData,
    loadCompanyInfo,
    loadTeamMembers,
    loadServices,
    loadStats,
    loadTimeline
  };
};
