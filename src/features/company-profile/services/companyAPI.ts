import { 
  CompanyInfo, 
  TeamMember, 
  Service, 
  Project, 
  Testimonial, 
  CompanyStats, 
  Timeline, 
  Partnership 
} from '../types';

// Mock company data
const MOCK_COMPANY_INFO: CompanyInfo = {
  name: 'Hidroimplementos SPA',
  description: 'Empresa líder en soluciones de riego y bombeo con más de 15 años de experiencia en el mercado chileno.',
  mission: 'Proporcionar soluciones integrales de riego y bombeo que optimicen el uso del agua y mejoren la productividad de nuestros clientes.',
  vision: 'Ser la empresa de referencia en Chile para sistemas de riego eficientes y sostenibles.',
  values: [
    'Calidad en productos y servicios',
    'Innovación tecnológica',
    'Compromiso ambiental',
    'Excelencia en atención al cliente',
    'Responsabilidad social'
  ],
  foundedYear: 2008,
  employees: '25-50',
  location: 'Santiago, Chile',
  certifications: [
    'ISO 9001:2015',
    'Certificación KSB',
    'Certificación Grundfos',
    'SENCE'
  ],
  awards: [
    {
      id: '1',
      title: 'Mejor Distribuidor KSB Chile',
      year: 2023,
      organization: 'KSB Chile',
      description: 'Reconocimiento por excelencia en ventas y servicio técnico'
    },
    {
      id: '2',
      title: 'Empresa Sustentable',
      year: 2022,
      organization: 'Cámara de Comercio',
      description: 'Premio por implementación de prácticas sustentables'
    }
  ]
};

const MOCK_TEAM_MEMBERS: TeamMember[] = [
  {
    id: '1',
    name: 'Carlos Mendoza',
    position: 'Gerente General',
    department: 'Dirección',
    bio: 'Ingeniero Civil con más de 20 años de experiencia en sistemas de riego.',
    image: '/team/carlos-mendoza.jpg',
    email: '<EMAIL>',
    specialties: ['Gestión empresarial', 'Sistemas de riego', 'Desarrollo de negocios'],
    experience: '20+ años'
  },
  {
    id: '2',
    name: 'María González',
    position: 'Jefa de Ventas',
    department: 'Comercial',
    bio: 'Especialista en soluciones comerciales para el sector agrícola.',
    image: '/team/maria-gonzalez.jpg',
    specialties: ['Ventas técnicas', 'Atención al cliente', 'Sector agrícola'],
    experience: '12 años'
  },
  {
    id: '3',
    name: 'Roberto Silva',
    position: 'Jefe Técnico',
    department: 'Técnico',
    bio: 'Ingeniero especializado en sistemas de bombeo y automatización.',
    image: '/team/roberto-silva.jpg',
    specialties: ['Sistemas de bombeo', 'Automatización', 'Mantenimiento'],
    experience: '15 años'
  }
];

const MOCK_SERVICES: Service[] = [
  {
    id: '1',
    title: 'Diseño de Sistemas de Riego',
    description: 'Diseñamos sistemas de riego personalizados para maximizar la eficiencia hídrica.',
    icon: '🎯',
    features: [
      'Análisis de suelo y cultivo',
      'Cálculo hidráulico',
      'Selección de equipos',
      'Planos técnicos'
    ],
    benefits: [
      'Ahorro de agua hasta 40%',
      'Mejor distribución del riego',
      'Reducción de costos operativos'
    ]
  },
  {
    id: '2',
    title: 'Instalación y Puesta en Marcha',
    description: 'Instalación profesional con garantía y capacitación incluida.',
    icon: '🔧',
    features: [
      'Instalación certificada',
      'Pruebas de funcionamiento',
      'Capacitación del personal',
      'Documentación técnica'
    ],
    benefits: [
      'Instalación garantizada',
      'Personal capacitado',
      'Soporte técnico continuo'
    ]
  },
  {
    id: '3',
    title: 'Mantenimiento Preventivo',
    description: 'Programas de mantenimiento para asegurar el óptimo funcionamiento.',
    icon: '🛠️',
    features: [
      'Inspecciones periódicas',
      'Limpieza de filtros',
      'Calibración de equipos',
      'Reporte de estado'
    ],
    benefits: [
      'Mayor vida útil de equipos',
      'Prevención de fallas',
      'Optimización del rendimiento'
    ]
  }
];

const MOCK_STATS: CompanyStats = {
  projectsCompleted: 500,
  clientsSatisfied: 350,
  yearsExperience: 15,
  teamMembers: 35,
  certifications: 4
};

/**
 * Get company information
 */
export const getCompanyInfo = async (): Promise<CompanyInfo> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return MOCK_COMPANY_INFO;
};

/**
 * Get team members
 */
export const getTeamMembers = async (): Promise<TeamMember[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return MOCK_TEAM_MEMBERS;
};

/**
 * Get services
 */
export const getServices = async (): Promise<Service[]> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  return MOCK_SERVICES;
};

/**
 * Get company statistics
 */
export const getCompanyStats = async (): Promise<CompanyStats> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  return MOCK_STATS;
};

/**
 * Get company timeline
 */
export const getTimeline = async (): Promise<Timeline[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return [
    {
      year: 2008,
      title: 'Fundación de la empresa',
      description: 'Inicio de operaciones especializándose en bombas de agua',
      milestone: true
    },
    {
      year: 2012,
      title: 'Expansión de servicios',
      description: 'Incorporación de sistemas de riego automatizado',
      milestone: false
    },
    {
      year: 2015,
      title: 'Certificación ISO 9001',
      description: 'Obtención de certificación de calidad internacional',
      milestone: true
    },
    {
      year: 2018,
      title: 'Alianza con KSB',
      description: 'Distribución exclusiva de bombas KSB en la región',
      milestone: true
    },
    {
      year: 2020,
      title: 'Digitalización',
      description: 'Implementación de herramientas digitales y e-commerce',
      milestone: false
    },
    {
      year: 2023,
      title: 'Reconocimiento KSB',
      description: 'Premio al mejor distribuidor KSB Chile',
      milestone: true
    }
  ];
};
