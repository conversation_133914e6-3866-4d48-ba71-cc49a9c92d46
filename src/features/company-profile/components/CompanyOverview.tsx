import React from 'react';
import { motion } from 'framer-motion';
import { Award, Calendar, MapPin, Users } from 'lucide-react';
import { CompanyInfo, CompanyStats } from '../types';

interface CompanyOverviewProps {
  companyInfo: CompanyInfo;
  stats: CompanyStats;
}

export const CompanyOverview: React.FC<CompanyOverviewProps> = ({
  companyInfo,
  stats
}) => {
  const statsData = [
    {
      icon: Calendar,
      label: 'Años de experiencia',
      value: stats.yearsExperience,
      suffix: '+'
    },
    {
      icon: Users,
      label: 'Proyectos completados',
      value: stats.projectsCompleted,
      suffix: '+'
    },
    {
      icon: Award,
      label: 'Clientes satisfechos',
      value: stats.clientsSatisfied,
      suffix: '+'
    },
    {
      icon: MapPin,
      label: 'Miembros del equipo',
      value: stats.teamMembers,
      suffix: ''
    }
  ];

  return (
    <section className="py-20 bg-white dark:bg-slate-900">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Sobre {companyInfo.name}
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            {companyInfo.description}
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {statsData.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full mb-4">
                <stat.icon className="w-8 h-8 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                {stat.value}{stat.suffix}
              </div>
              <div className="text-slate-600 dark:text-slate-400">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Mission, Vision, Values */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Mission */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
              Misión
            </h3>
            <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
              {companyInfo.mission}
            </p>
          </motion.div>

          {/* Vision */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
              Visión
            </h3>
            <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
              {companyInfo.vision}
            </p>
          </motion.div>

          {/* Values */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
              Valores
            </h3>
            <ul className="space-y-2">
              {companyInfo.values.map((value, index) => (
                <li
                  key={index}
                  className="flex items-center text-slate-600 dark:text-slate-400"
                >
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3 flex-shrink-0" />
                  {value}
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Certifications and Awards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-16">
          {/* Certifications */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
              Certificaciones
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {companyInfo.certifications.map((cert, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-slate-700 rounded-lg p-4 text-center"
                >
                  <Award className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                  <span className="text-sm font-medium text-slate-900 dark:text-white">
                    {cert}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Awards */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
              Reconocimientos
            </h3>
            <div className="space-y-4">
              {companyInfo.awards.map((award) => (
                <div
                  key={award.id}
                  className="bg-white dark:bg-slate-700 rounded-lg p-4"
                >
                  <div className="flex items-start space-x-3">
                    <Award className="w-6 h-6 text-orange-500 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-slate-900 dark:text-white">
                        {award.title}
                      </h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        {award.organization} • {award.year}
                      </p>
                      <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                        {award.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
