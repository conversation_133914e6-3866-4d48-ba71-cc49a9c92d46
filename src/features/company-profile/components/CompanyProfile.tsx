import React from 'react';
import { motion } from 'framer-motion';
import { Spinner } from '@shared/ui';
import { CompanyOverview } from './CompanyOverview';
import { useCompanyProfile } from '../hooks';

export const CompanyProfile: React.FC = () => {
  const {
    companyInfo,
    stats,
    isLoading,
    error
  } = useCompanyProfile();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-slate-600 dark:text-slate-400">
            Cargando información de la empresa...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  if (!companyInfo || !stats) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <CompanyOverview 
        companyInfo={companyInfo} 
        stats={stats} 
      />
    </motion.div>
  );
};
