import { PumpSearchParams, PumpResult } from '../types';

// Mock database of pump products
const PUMP_DATABASE: PumpResult[] = [
  {
    id: 1,
    modelo: 'KSB Etanorm 125-100-250',
    tipo: 'Bomba centrífuga industrial',
    altura_max: 50,
    caudal_max: 100,
    potencia: '15 kW',
    precio: 2850000,
    imagen: '/bomba-centrifuga-ksb.webp',
    aplicaciones: ['industrial', 'abastecimiento', 'calefaccion']
  },
  {
    id: 2,
    modelo: 'Grundfos SP 30-7',
    tipo: 'Bomba sumergible',
    altura_max: 60,
    caudal_max: 80,
    potencia: '7.5 kW',
    precio: 1850000,
    imagen: '/bomba-sumergible-grundfos.webp',
    aplicaciones: ['abastecimiento', 'riego', 'achique']
  },
  {
    id: 3,
    modelo: 'Sistema Goteo Premium',
    tipo: 'Sistema de riego automático',
    altura_max: 15,
    caudal_max: 25,
    potencia: '0.5 kW',
    precio: 450000,
    imagen: '/sistema-goteo-premium.webp',
    aplicaciones: ['riego']
  },
  {
    id: 4,
    modelo: 'Rain Bird R50-PJ',
    tipo: 'Aspersor rotativo profesional',
    altura_max: 25,
    caudal_max: 40,
    potencia: 'N/A',
    precio: 125000,
    imagen: '/aspersor-rotativo.webp',
    aplicaciones: ['riego']
  },
  {
    id: 5,
    modelo: 'Hunter PGV-101G',
    tipo: 'Válvula solenoide',
    altura_max: 100,
    caudal_max: 15,
    potencia: '24V AC',
    precio: 85000,
    imagen: '/valvula-solenoide-hunter.webp',
    aplicaciones: ['riego', 'industrial']
  }
];

/**
 * Search for pumps based on advanced parameters
 */
export const searchPumps = async (params: PumpSearchParams): Promise<PumpResult[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const alturaNum = parseFloat(params.altura);
  const caudalNum = parseFloat(params.caudal);
  
  // Basic validation
  if (alturaNum <= 0 || caudalNum <= 0 || !params.aplicacion) {
    return [];
  }
  
  // 70% chance of finding results
  if (Math.random() <= 0.3) {
    return [];
  }
  
  // Filter products by application
  const filteredProducts = PUMP_DATABASE.filter(product =>
    product.aplicaciones.includes(params.aplicacion)
  );
  
  // Further filter by technical specifications
  const matchingProducts = filteredProducts.filter(product => {
    const heightMatch = product.altura_max >= alturaNum * 0.8; // 80% tolerance
    const flowMatch = product.caudal_max >= caudalNum * 0.8; // 80% tolerance
    return heightMatch && flowMatch;
  });
  
  // If no exact matches, return some products from the application category
  const finalResults = matchingProducts.length > 0 
    ? matchingProducts 
    : filteredProducts.slice(0, 2);
  
  // Return 2-3 random products
  return finalResults
    .sort(() => Math.random() - 0.5)
    .slice(0, Math.floor(Math.random() * 2) + 2);
};

/**
 * Get product recommendations based on application
 */
export const getRecommendations = async (application: string): Promise<PumpResult[]> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const recommendations = PUMP_DATABASE
    .filter(product => product.aplicaciones.includes(application))
    .slice(0, 3);
    
  return recommendations;
};
