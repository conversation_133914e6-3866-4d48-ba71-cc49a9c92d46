export interface PumpSearchParams {
  aplicacion: string;
  tipo_sistema: string;
  altura: string;
  unidad_altura: string;
  caudal: string;
  unidad_caudal: string;
  temperatura: string;
  unidad_temp: string;
  fluido_bombeado: string;
  control_velocidad: string;
}

export interface PumpResult {
  id: number;
  modelo: string;
  tipo: string;
  altura_max: number;
  caudal_max: number;
  potencia: string;
  precio: number;
  imagen: string;
  aplicaciones: string[];
}

export interface ApplicationOption {
  id: string;
  label: string;
  icon: string;
  description: string;
}

export interface SearchFormData {
  selectedApp: string;
  tipoSistema: string;
  altura: string;
  unidadAltura: string;
  caudal: string;
  unidadCaudal: string;
  temperatura: string;
  unidadTemp: string;
  fluidoBombeado: string;
  controlVelocidad: string;
}

export interface SearchState {
  isSearching: boolean;
  mostrarResultados: boolean;
  mostrarSinResultados: boolean;
  resultados: PumpResult[];
  error: string;
}
