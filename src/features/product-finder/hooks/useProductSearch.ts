import { useState, useCallback } from 'react';
import { PumpSearchParams, PumpResult, SearchState } from '../types';
import { searchPumps } from '../services';

export const useProductSearch = () => {
  const [searchState, setSearchState] = useState<SearchState>({
    isSearching: false,
    mostrarResultados: false,
    mostrarSinResultados: false,
    resultados: [],
    error: ''
  });

  const executeSearch = useCallback(async (params: PumpSearchParams) => {
    setSearchState(prev => ({
      ...prev,
      isSearching: true,
      error: '',
      mostrarSinResultados: false,
      mostrarResultados: false
    }));

    try {
      const results = await searchPumps(params);
      
      if (results && results.length > 0) {
        setSearchState(prev => ({
          ...prev,
          resultados: results,
          mostrarResultados: true,
          isSearching: false
        }));
        
        // Smooth scroll to results
        setTimeout(() => {
          document.getElementById('resultados-section')?.scrollIntoView({ 
            behavior: 'smooth' 
          });
        }, 100);
      } else {
        setSearchState(prev => ({
          ...prev,
          mostrarSinResultados: true,
          isSearching: false
        }));
      }
    } catch (error) {
      console.error('Error en búsqueda:', error);
      setSearchState(prev => ({
        ...prev,
        error: 'Error al buscar productos. Intenta nuevamente.',
        isSearching: false
      }));
    }
  }, []);

  const resetSearch = useCallback(() => {
    setSearchState({
      isSearching: false,
      mostrarResultados: false,
      mostrarSinResultados: false,
      resultados: [],
      error: ''
    });
  }, []);

  return {
    ...searchState,
    executeSearch,
    resetSearch
  };
};
