import React from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, MessageCircle } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa';
import { Button } from '@shared/ui';
import { formatPhoneForWhatsApp } from '@shared/utils';
import { COMPANY_INFO } from '@shared/constants';
import { SearchFormData } from '../types';

interface NoResultsProps {
  searchParams: SearchFormData;
}

const getAplicacionText = (app: string): string => {
  const aplicaciones: Record<string, string> = {
    'riego': 'Riego',
    'abastecimiento': 'Abastecimiento de agua',
    'industrial': 'Uso industrial',
    'achique': 'Achique y drenaje',
    'calefaccion': 'Calefacción',
    'piscina': 'Piscina'
  };
  return aplicaciones[app] || app;
};

const getFluidoText = (fluido: string): string => {
  const fluidos: Record<string, string> = {
    'agua_limpia': 'Agua limpia',
    'agua_sucia': 'Agua con sólidos',
    'agua_salada': 'Agua salada',
    'quimicos': 'Químicos',
    'aceites': 'Aceites',
    'otros': 'Otros fluidos'
  };
  return fluidos[fluido] || fluido;
};

export const NoResults: React.FC<NoResultsProps> = ({ searchParams }) => {
  const handleConsultExpert = () => {
    const specifications = `
🔍 BÚSQUEDA DE BOMBA - HIDROIMPLEMENTOS

📋 Especificaciones técnicas:
- Aplicación: ${getAplicacionText(searchParams.selectedApp)}
- Tipo sistema: ${searchParams.tipoSistema || 'No especificado'}
- Altura total: ${searchParams.altura} ${searchParams.unidadAltura}
- Caudal total: ${searchParams.caudal} ${searchParams.unidadCaudal}  
- Temperatura: ${searchParams.temperatura}°${searchParams.unidadTemp}
- Fluido: ${getFluidoText(searchParams.fluidoBombeado)}
- Control velocidad: ${searchParams.controlVelocidad}

❓ No encontré productos que coincidan exactamente con estos parámetros. ¿Pueden ayudarme a encontrar la bomba ideal o una alternativa similar?
    `.trim();
    
    const phoneNumber = formatPhoneForWhatsApp(COMPANY_INFO.phone);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(specifications)}`;
    window.open(whatsappURL, '_blank');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mt-12"
    >
      <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl text-center">
        <div className="mb-6">
          <AlertTriangle className="mx-auto h-16 w-16 text-orange-500 mb-4" />
          <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            No encontramos productos exactos
          </h3>
          <p className="text-slate-600 dark:text-slate-400">
            No hay productos en nuestro catálogo que coincidan exactamente con tus especificaciones.
          </p>
        </div>

        {/* Search Parameters Summary */}
        <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 mb-6">
          <h4 className="font-semibold text-slate-900 dark:text-white mb-3">
            Tus especificaciones:
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-left">
            <div><strong>Aplicación:</strong> {getAplicacionText(searchParams.selectedApp)}</div>
            <div><strong>Tipo sistema:</strong> {searchParams.tipoSistema || 'No especificado'}</div>
            <div><strong>Altura:</strong> {searchParams.altura} {searchParams.unidadAltura}</div>
            <div><strong>Caudal:</strong> {searchParams.caudal} {searchParams.unidadCaudal}</div>
            <div><strong>Temperatura:</strong> {searchParams.temperatura}°{searchParams.unidadTemp}</div>
            <div><strong>Fluido:</strong> {getFluidoText(searchParams.fluidoBombeado)}</div>
          </div>
        </div>

        {/* Expert Consultation */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-slate-900 dark:text-white">
            ¿Necesitas ayuda personalizada?
          </h4>
          <p className="text-slate-600 dark:text-slate-400">
            Nuestros expertos pueden ayudarte a encontrar la bomba perfecta para tu proyecto, 
            incluso si no está en nuestro catálogo online.
          </p>
          
          <Button
            onClick={handleConsultExpert}
            variant="primary"
            size="lg"
            leftIcon={<FaWhatsapp size={24} />}
            className="bg-green-600 hover:bg-green-700"
          >
            Consultar con experto vía WhatsApp
          </Button>
        </div>

        {/* Additional Options */}
        <div className="mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
          <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
            También puedes:
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => window.open(COMPANY_INFO.shopifyStore, '_blank')}
              variant="outline"
              size="md"
            >
              Ver catálogo completo
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant="ghost"
              size="md"
            >
              Realizar nueva búsqueda
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
