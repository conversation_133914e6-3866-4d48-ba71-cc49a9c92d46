import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa';
import { Button } from '@shared/ui';
import { formatCurrency, formatPhoneForWhatsApp } from '@shared/utils';
import { COMPANY_INFO } from '@shared/constants';
import { PumpResult } from '../types';

interface ResultsGridProps {
  results: PumpResult[];
  searchParams?: any;
}

export const ResultsGrid: React.FC<ResultsGridProps> = ({ results, searchParams }) => {
  const handleWhatsAppConsult = (pump: PumpResult) => {
    const message = `Hola! Me interesa la bomba ${pump.modelo} (${pump.tipo}). ¿Pueden darme más información sobre disponibilidad y características técnicas?`;
    const phoneNumber = formatPhoneForWhatsApp(COMPANY_INFO.phone);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappURL, '_blank');
  };

  const handleViewCatalog = () => {
    window.open(COMPANY_INFO.shopifyStore, '_blank');
  };

  return (
    <motion.div
      id="resultados-section"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mt-12"
    >
      <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
            Resultados encontrados ({results.length})
          </h3>
          <div className="text-sm text-slate-600 dark:text-slate-400">
            Productos que coinciden con tus especificaciones
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {results.map((pump, index) => (
            <motion.div
              key={pump.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 hover:shadow-lg transition-shadow"
            >
              <div className="aspect-square bg-white dark:bg-slate-600 rounded-lg mb-4 flex items-center justify-center overflow-hidden">
                <img
                  src={pump.imagen}
                  alt={pump.modelo}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-pump.jpg';
                  }}
                />
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-slate-900 dark:text-white text-lg">
                  {pump.modelo}
                </h4>
                
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  {pump.tipo}
                </p>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">Altura máx:</span>
                    <span className="font-medium">{pump.altura_max} mca</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">Caudal máx:</span>
                    <span className="font-medium">{pump.caudal_max} L/min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">Potencia:</span>
                    <span className="font-medium">{pump.potencia}</span>
                  </div>
                </div>

                <div className="pt-3 border-t border-slate-200 dark:border-slate-600">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-lg font-bold text-green-600">
                      {formatCurrency(pump.precio)}
                    </span>
                  </div>
                  
                  <Button
                    onClick={() => handleWhatsAppConsult(pump)}
                    variant="primary"
                    size="sm"
                    leftIcon={<FaWhatsapp size={16} />}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    Consultar
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View Catalog Button */}
        <div className="text-center">
          <Button
            onClick={handleViewCatalog}
            variant="outline"
            size="lg"
            leftIcon={<ExternalLink size={20} />}
          >
            Ver más productos en nuestro catálogo
          </Button>
        </div>
      </div>
    </motion.div>
  );
};
