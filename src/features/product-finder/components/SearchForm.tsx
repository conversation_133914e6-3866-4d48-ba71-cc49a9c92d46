import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, Loader } from 'lucide-react';
import { Button, Input } from '@shared/ui';
import { SearchFormData, ApplicationOption } from '../types';

interface SearchFormProps {
  onSearch: (data: SearchFormData) => void;
  isSearching: boolean;
}

const aplicaciones: ApplicationOption[] = [
  { id: 'riego', label: 'Riego', icon: '🌱', description: 'Sistemas de riego agrícola y jardín' },
  { id: 'abastecimiento', label: 'Abastecimiento', icon: '🏠', description: 'Suministro de agua doméstico e industrial' },
  { id: 'industrial', label: 'Industrial', icon: '🏭', description: 'Procesos industriales y manufactura' },
  { id: 'achique', label: 'Achique', icon: '⚡', description: 'Drenaje y evacuación de agua' },
  { id: 'calefaccion', label: 'Calefacción', icon: '🔥', description: 'Sistemas de calefacción y climatización' },
  { id: 'piscina', label: 'Piscina', icon: '🏊', description: 'Filtración y circulación de piscinas' }
];

export const SearchForm: React.FC<SearchFormProps> = ({ onSearch, isSearching }) => {
  const [formData, setFormData] = useState<SearchFormData>({
    selectedApp: '',
    tipoSistema: '',
    altura: '',
    unidadAltura: 'mca',
    caudal: '',
    unidadCaudal: 'L/min',
    temperatura: '20',
    unidadTemp: 'C',
    fluidoBombeado: 'agua_limpia',
    controlVelocidad: 'no'
  });

  const isFormValid = formData.selectedApp && formData.altura && formData.caudal;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid && !isSearching) {
      onSearch(formData);
    }
  };

  const updateFormData = (field: keyof SearchFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <motion.form
      onSubmit={handleSubmit}
      className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Application Selection */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
          Aplicación *
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {aplicaciones.map((app) => (
            <motion.button
              key={app.id}
              type="button"
              onClick={() => updateFormData('selectedApp', app.id)}
              className={`p-4 rounded-xl border-2 transition-all text-left ${
                formData.selectedApp === app.id
                  ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                  : 'border-slate-200 dark:border-slate-600 hover:border-orange-300'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="text-2xl mb-2">{app.icon}</div>
              <div className="font-medium text-slate-900 dark:text-white">{app.label}</div>
              <div className="text-sm text-slate-600 dark:text-slate-400">{app.description}</div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Technical Specifications */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* System Type */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Tipo de sistema
          </label>
          <select
            value={formData.tipoSistema}
            onChange={(e) => updateFormData('tipoSistema', e.target.value)}
            className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100"
          >
            <option value="">Seleccionar tipo</option>
            <option value="superficie">Superficie</option>
            <option value="sumergible">Sumergible</option>
            <option value="inline">En línea</option>
            <option value="booster">Booster</option>
          </select>
        </div>

        {/* Height */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Altura total *
          </label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={formData.altura}
              onChange={(e) => updateFormData('altura', e.target.value)}
              placeholder="Ej: 25"
              className="flex-1"
            />
            <select
              value={formData.unidadAltura}
              onChange={(e) => updateFormData('unidadAltura', e.target.value)}
              className="px-3 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800"
            >
              <option value="mca">mca</option>
              <option value="bar">bar</option>
              <option value="psi">psi</option>
            </select>
          </div>
        </div>

        {/* Flow Rate */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Caudal total *
          </label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={formData.caudal}
              onChange={(e) => updateFormData('caudal', e.target.value)}
              placeholder="Ej: 50"
              className="flex-1"
            />
            <select
              value={formData.unidadCaudal}
              onChange={(e) => updateFormData('unidadCaudal', e.target.value)}
              className="px-3 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800"
            >
              <option value="L/min">L/min</option>
              <option value="L/h">L/h</option>
              <option value="m³/h">m³/h</option>
            </select>
          </div>
        </div>

        {/* Temperature */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Temperatura del fluido
          </label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={formData.temperatura}
              onChange={(e) => updateFormData('temperatura', e.target.value)}
              placeholder="20"
              className="flex-1"
            />
            <select
              value={formData.unidadTemp}
              onChange={(e) => updateFormData('unidadTemp', e.target.value)}
              className="px-3 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800"
            >
              <option value="C">°C</option>
              <option value="F">°F</option>
            </select>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!isFormValid || isSearching}
          isLoading={isSearching}
          leftIcon={isSearching ? <Loader className="animate-spin" size={20} /> : <Search size={20} />}
          className="px-8 py-3"
        >
          {isSearching ? 'Buscando...' : 'Mostrar resultados'}
        </Button>
      </div>
    </motion.form>
  );
};
