import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SearchForm } from './SearchForm';
import { ResultsGrid } from './ResultsGrid';
import { NoResults } from './NoResults';
import { useProductSearch } from '../hooks';
import { SearchFormData, PumpSearchParams } from '../types';

export const AdvancedPumpFinder: React.FC = () => {
  const {
    isSearching,
    mostrarResultados,
    mostrarSinResultados,
    resultados,
    error,
    executeSearch
  } = useProductSearch();

  const handleSearch = async (formData: SearchFormData) => {
    const searchParams: PumpSearchParams = {
      aplicacion: formData.selectedApp,
      tipo_sistema: formData.tipoSistema,
      altura: formData.altura,
      unidad_altura: formData.unidadAltura,
      caudal: formData.caudal,
      unidad_caudal: formData.unidadCaudal,
      temperatura: formData.temperatura,
      unidad_temp: formData.unidadTemp,
      fluido_bombeado: formData.fluidoBombeado,
      control_velocidad: formData.controlVelocidad
    };

    await executeSearch(searchParams);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Buscador Avanzado de Bombas
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            Encuentra la bomba perfecta para tu proyecto con nuestro sistema inteligente de búsqueda.
            Especifica tus requerimientos técnicos y te mostraremos las mejores opciones.
          </p>
        </motion.div>

        {/* Search Form */}
        <SearchForm onSearch={handleSearch} isSearching={isSearching} />

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
          >
            <p className="text-red-700 dark:text-red-400 text-center">{error}</p>
          </motion.div>
        )}

        {/* Results */}
        <AnimatePresence mode="wait">
          {mostrarResultados && (
            <ResultsGrid results={resultados} />
          )}

          {mostrarSinResultados && (
            <NoResults searchParams={{
              selectedApp: '',
              tipoSistema: '',
              altura: '',
              unidadAltura: 'mca',
              caudal: '',
              unidadCaudal: 'L/min',
              temperatura: '20',
              unidadTemp: 'C',
              fluidoBombeado: 'agua_limpia',
              controlVelocidad: 'no'
            }} />
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};
