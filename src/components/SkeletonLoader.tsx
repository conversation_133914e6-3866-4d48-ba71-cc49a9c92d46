import React from 'react';
import { motion } from 'framer-motion';

interface SkeletonLoaderProps {
  className?: string;
  variant?: 'rectangular' | 'circular' | 'text' | 'card';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  className = '',
  variant = 'rectangular',
  width = '100%',
  height = '100%',
  lines = 3
}) => {
  const baseClasses = 'bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 dark:from-slate-700 dark:via-slate-600 dark:to-slate-700';
  
  const shimmerAnimation = {
    backgroundPosition: ['-200% 0', '200% 0'],
  };

  const shimmerTransition = {
    duration: 1.5,
    repeat: Infinity,
    ease: 'linear',
  };

  if (variant === 'text') {
    return (
      <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <motion.div
            key={index}
            className={`${baseClasses} rounded h-4`}
            style={{
              width: index === lines - 1 ? '75%' : '100%',
              backgroundSize: '400% 100%',
            }}
            animate={shimmerAnimation}
            transition={shimmerTransition}
          />
        ))}
      </div>
    );
  }

  if (variant === 'circular') {
    return (
      <motion.div
        className={`${baseClasses} rounded-full ${className}`}
        style={{
          width,
          height,
          backgroundSize: '400% 100%',
        }}
        animate={shimmerAnimation}
        transition={shimmerTransition}
      />
    );
  }

  if (variant === 'card') {
    return (
      <div className={`${className}`}>
        <motion.div
          className={`${baseClasses} rounded-lg mb-4`}
          style={{
            width: '100%',
            height: '200px',
            backgroundSize: '400% 100%',
          }}
          animate={shimmerAnimation}
          transition={shimmerTransition}
        />
        <div className="space-y-2">
          <motion.div
            className={`${baseClasses} rounded h-4`}
            style={{
              width: '100%',
              backgroundSize: '400% 100%',
            }}
            animate={shimmerAnimation}
            transition={shimmerTransition}
          />
          <motion.div
            className={`${baseClasses} rounded h-4`}
            style={{
              width: '75%',
              backgroundSize: '400% 100%',
            }}
            animate={shimmerAnimation}
            transition={shimmerTransition}
          />
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={`${baseClasses} rounded ${className}`}
      style={{
        width,
        height,
        backgroundSize: '400% 100%',
      }}
      animate={shimmerAnimation}
      transition={shimmerTransition}
    />
  );
};
