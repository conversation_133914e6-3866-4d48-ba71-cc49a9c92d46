import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { FaPhone, FaPhoneAlt } from 'react-icons/fa';
import { MdCall } from 'react-icons/md';

export const PhoneButton: React.FC = () => {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleClick = () => {
    window.open('tel:+56912345678', '_self');
  };

  return (
    <div className="fixed bottom-6 right-24 z-50">
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, x: 20, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 20, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="absolute bottom-16 right-0 bg-white dark:bg-slate-800 rounded-xl shadow-xl p-4 whitespace-nowrap border border-slate-200 dark:border-slate-700 backdrop-blur-sm"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <FaPhone className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-slate-900 dark:text-white">
                  ¡Llámanos ahora!
                </span>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  +56 9 1234 5678
                </span>
              </div>
              <button
                onClick={() => setShowTooltip(false)}
                className="p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full transition-colors"
              >
                <X className="w-3 h-3 text-slate-500" />
              </button>
            </div>
            <div className="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-3 h-3 bg-white dark:bg-slate-800 border-r border-b border-slate-200 dark:border-slate-700"></div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        onClick={handleClick}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all relative group"
        whileHover={{ scale: 1.1, rotate: [0, 5, -5, 0] }}
        whileTap={{ scale: 0.9 }}
        animate={{
          boxShadow: [
            "0 0 0 0 rgba(59, 130, 246, 0.7)",
            "0 0 0 15px rgba(59, 130, 246, 0)",
            "0 0 0 30px rgba(59, 130, 246, 0)",
          ],
        }}
        transition={{
          boxShadow: {
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          },
          hover: {
            type: "spring",
            stiffness: 400,
            damping: 10
          }
        }}
      >
        <motion.div
          animate={{ rotate: [0, 15, -15, 0] }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <FaPhoneAlt className="w-6 h-6 group-hover:scale-110 transition-transform" />
        </motion.div>
        
        {/* Call Status Indicator */}
        <motion.div 
          className="absolute -top-1 -right-1 w-4 h-4 bg-blue-400 rounded-full border-2 border-white"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [1, 0.7, 1]
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Available Badge */}
        <motion.div 
          className="absolute -top-2 -left-2 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 500 }}
        >
          <span className="text-xs text-white font-bold">📞</span>
        </motion.div>
      </motion.button>
    </div>
  );
};
