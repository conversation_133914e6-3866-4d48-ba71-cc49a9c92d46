import { AnimatePresence, motion } from 'framer-motion';
import {
    <PERSON><PERSON><PERSON><PERSON>gle,
    ArrowRight,
    CheckCircle,
    ExternalLink,
    Loader,
    Search
} from 'lucide-react';
import React, { useState } from 'react';
import {
    FaWhatsapp,
    FaIndustry,
    FaHome,
    FaTint,
    FaSeedling,
    FaCogs,
    FaThermometerHalf,
    FaRulerVertical,
    FaTachometerAlt,
    FaFlask,
    FaTools
} from 'react-icons/fa';

interface BusquedaParams {
  aplicacion: string;
  tipo_sistema: string;
  altura: string;
  unidad_altura: string;
  caudal: string;
  unidad_caudal: string;
  temperatura: string;
  unidad_temp: string;
  fluido_bombeado: string;
  control_velocidad: string;
}

export const AdvancedPumpFinder: React.FC = () => {
  // Estados del formulario
  const [selectedApp, setSelectedApp] = useState('');
  const [tipoSistema, setTipoSistema] = useState('');
  const [altura, setAltura] = useState('');
  const [unidadAltura, setUnidadAltura] = useState('m');
  const [caudal, setCaudal] = useState('');
  const [unidadCaudal, setUnidadCaudal] = useState('m3h');
  const [temperatura, setTemperatura] = useState('20');
  const [unidadTemp, setUnidadTemp] = useState('C');
  const [fluidoBombeado, setFluidoBombeado] = useState('');
  const [controlVelocidad, setControlVelocidad] = useState('todas');

  // Estados de UI
  const [isSearching, setIsSearching] = useState(false);
  const [mostrarResultados, setMostrarResultados] = useState(false);
  const [mostrarSinResultados, setMostrarSinResultados] = useState(false);
  const [resultados, setResultados] = useState<any[]>([]);
  const [error, setError] = useState('');

  // Opciones de aplicación
  const aplicaciones = [
    {
      id: 'calefaccion',
      label: 'Calefacción/climatización/refrigeración',
      value: 'calefaccion'
    },
    {
      id: 'abastecimiento', 
      label: 'Abastecimiento de agua',
      value: 'abastecimiento'
    },
    {
      id: 'achique',
      label: 'Achique', 
      value: 'achique'
    },
    {
      id: 'riego',
      label: 'Sistemas de riego',
      value: 'riego'
    },
    {
      id: 'industrial',
      label: 'Aplicaciones industriales',
      value: 'industrial'
    }
  ];

  // Opciones de fluidos
  const fluidos = [
    { value: 'agua_limpia', label: 'Agua limpia' },
    { value: 'agua_calefaccion_110', label: 'Agua de calefacción ≤ +110°C' },
    { value: 'agua_calefaccion_140', label: 'Agua de calefacción ≤ +140°C' },
    { value: 'agua_etilenglicol_50', label: 'Agua, etilenglicol (pH ≥ 7,5) 50%' },
    { value: 'agua_propilenglicol_50', label: 'Agua, propilenglicol (pH ≥ 7,5) 50%' },
    { value: 'agua_potable', label: 'Agua, agua potable' },
    { value: 'agua_residual', label: 'Agua residual' },
    { value: 'aceites_hidraulicos', label: 'Aceites hidráulicos' },
    { value: 'productos_quimicos', label: 'Productos químicos' },
    { value: 'agua_marina', label: 'Agua de mar' }
  ];

  // Validación del formulario
  const isFormValid = selectedApp && altura && caudal && temperatura;

  // Función para obtener texto de aplicación
  const getAplicacionText = (value: string) => {
    const app = aplicaciones.find(a => a.value === value);
    return app ? app.label : value;
  };

  // Función para obtener texto de fluido
  const getFluidoText = (value: string) => {
    const fluido = fluidos.find(f => f.value === value);
    return fluido ? fluido.label : 'No especificado';
  };

  // Simulación de búsqueda en base de datos
  const buscarBombasAvanzado = async (params: BusquedaParams): Promise<any[]> => {
    // Simular delay de búsqueda
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simular resultados basados en parámetros
    const alturaNum = parseFloat(params.altura);
    const caudalNum = parseFloat(params.caudal);
    
    // Lógica de coincidencia simple
    if (alturaNum > 0 && caudalNum > 0 && params.aplicacion) {
      // 70% de probabilidad de encontrar resultados
      if (Math.random() > 0.3) {
        // Seleccionar productos basados en la aplicación
        const productosDisponibles = [
          {
            id: 1,
            modelo: 'KSB Etanorm 125-100-250',
            tipo: 'Bomba centrífuga industrial',
            altura_max: alturaNum * 1.1,
            caudal_max: caudalNum * 1.1,
            potencia: '15 kW',
            precio: 2850000,
            imagen: '/Bomba_Centrífuga_Industrial_KSB.webp',
            aplicaciones: ['industrial', 'abastecimiento', 'calefaccion']
          },
          {
            id: 2,
            modelo: 'Grundfos SP 30-7',
            tipo: 'Bomba sumergible',
            altura_max: alturaNum * 1.2,
            caudal_max: caudalNum * 0.9,
            potencia: '7.5 kW',
            precio: 1850000,
            imagen: '/Bomba_Sumergible_Grundfos.webp',
            aplicaciones: ['abastecimiento', 'riego', 'achique']
          },
          {
            id: 3,
            modelo: 'Sistema Goteo Premium',
            tipo: 'Sistema de riego automático',
            altura_max: 15,
            caudal_max: 25,
            potencia: '0.5 kW',
            precio: 450000,
            imagen: '/Sistema_Goteo_Automático_Premium.webp',
            aplicaciones: ['riego']
          },
          {
            id: 4,
            modelo: 'Rain Bird R50-PJ',
            tipo: 'Aspersor rotativo profesional',
            altura_max: 25,
            caudal_max: 40,
            potencia: 'N/A',
            precio: 125000,
            imagen: '/Aspersor_Rotativo_Profesional.webp',
            aplicaciones: ['riego']
          },
          {
            id: 5,
            modelo: 'Hunter PGV-101G',
            tipo: 'Válvula solenoide',
            altura_max: 100,
            caudal_max: 15,
            potencia: '24V AC',
            precio: 85000,
            imagen: '/Válvula_Solenoide_Hunter.webp',
            aplicaciones: ['riego', 'industrial']
          }
        ];

        // Filtrar productos por aplicación
        const productosFiltrados = productosDisponibles.filter(producto =>
          producto.aplicaciones.includes(params.aplicacion)
        );

        // Retornar 2-3 productos aleatorios que coincidan
        const productosSeleccionados = productosFiltrados
          .sort(() => Math.random() - 0.5)
          .slice(0, Math.floor(Math.random() * 2) + 2);

        return productosSeleccionados;
      }
    }
    
    return [];
  };

  // Manejar búsqueda
  const handleMostrarResultados = async () => {
    setIsSearching(true);
    setError('');
    setMostrarSinResultados(false);
    setMostrarResultados(false);
    
    const parametrosBusqueda: BusquedaParams = {
      aplicacion: selectedApp,
      tipo_sistema: tipoSistema,
      altura: altura,
      unidad_altura: unidadAltura,
      caudal: caudal,
      unidad_caudal: unidadCaudal,
      temperatura: temperatura,
      unidad_temp: unidadTemp,
      fluido_bombeado: fluidoBombeado,
      control_velocidad: controlVelocidad
    };
    
    try {
      const resultados = await buscarBombasAvanzado(parametrosBusqueda);
      
      if (resultados && resultados.length > 0) {
        setResultados(resultados);
        setMostrarResultados(true);
        // Scroll suave a resultados
        setTimeout(() => {
          document.getElementById('resultados-section')?.scrollIntoView({ 
            behavior: 'smooth' 
          });
        }, 100);
      } else {
        setMostrarSinResultados(true);
      }
    } catch (error) {
      console.error('Error en búsqueda:', error);
      setError('Error al buscar productos. Intenta nuevamente.');
    } finally {
      setIsSearching(false);
    }
  };

  // Consultar con experto vía WhatsApp
  const handleConsultarExperto = () => {
    const especificaciones = `
🔍 BÚSQUEDA DE BOMBA - HIDROIMPLEMENTOS

📋 Especificaciones técnicas:
- Aplicación: ${getAplicacionText(selectedApp)}
- Tipo sistema: ${tipoSistema || 'No especificado'}
- Altura total: ${altura} ${unidadAltura}
- Caudal total: ${caudal} ${unidadCaudal}  
- Temperatura: ${temperatura}°${unidadTemp}
- Fluido: ${getFluidoText(fluidoBombeado)}
- Control velocidad: ${controlVelocidad}

❓ No encontré productos que coincidan exactamente con estos parámetros. ¿Pueden ayudarme a encontrar la bomba ideal o una alternativa similar?
    `.trim();
    
    const whatsappURL = `https://wa.me/56996512719?text=${encodeURIComponent(especificaciones)}`;
    window.open(whatsappURL, '_blank');
  };

  return (
    <section className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
            Buscador Avanzado de Bombas
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            Encuentra la bomba perfecta para tu aplicación específica
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="max-w-5xl mx-auto p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-xl"
        >
          <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-8">
            ¿Cómo quieres que sea tu bomba?
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Columna izquierda: Aplicación */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white">
                  Elige tu aplicación.
                </h4>
                
                <div className="space-y-3">
                  {aplicaciones.map((option) => (
                    <motion.label 
                      key={option.id} 
                      className="flex items-start space-x-3 cursor-pointer p-3 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <input
                        type="radio"
                        name="aplicacion"
                        value={option.value}
                        checked={selectedApp === option.value}
                        onChange={(e) => setSelectedApp(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 mt-0.5"
                      />
                      <span className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">
                        {option.label}
                      </span>
                    </motion.label>
                  ))}
                </div>
                
                {/* Link catálogo */}
                <div className="pt-6 border-t border-slate-200 dark:border-slate-600">
                  <div className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                    ¿No has encontrado tu campo de aplicación?
                  </div>
                  <motion.button 
                    onClick={() => window.open('https://xzb7d0-uq.myshopify.com/password')}
                    className="flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium transition-colors"
                    whileHover={{ x: 5 }}
                  >
                    <ArrowRight className="mr-2" size={16} />
                    Descubre nuestro catálogo de productos
                  </motion.button>
                </div>
              </div>
            </div>
            
            {/* Columnas derecha: Formulario técnico */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                {/* Tipo de sistema */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Tipo de sistema
                  </label>
                  <select 
                    value={tipoSistema}
                    onChange={(e) => setTipoSistema(e.target.value)}
                    className="w-full p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  >
                    <option value="">Seleccionar...</option>
                    <option value="bomba_simple">Bomba simple</option>
                    <option value="bomba_doble">Bomba doble</option>
                    <option value="sistema_presion">Sistema de presión</option>
                    <option value="bomba_sumergible">Bomba sumergible</option>
                    <option value="bomba_centrifuga">Bomba centrífuga</option>
                    <option value="bomba_periferica">Bomba periférica</option>
                  </select>
                </div>

                {/* Total altura */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Total altura *
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="00"
                      min="0"
                      max="500"
                      value={altura}
                      onChange={(e) => setAltura(e.target.value)}
                      className="flex-1 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    />
                    <select 
                      value={unidadAltura}
                      onChange={(e) => setUnidadAltura(e.target.value)}
                      className="w-20 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    >
                      <option value="m">m</option>
                      <option value="fath">fath</option>
                      <option value="ft">ft</option>
                      <option value="yd">yd</option>
                    </select>
                  </div>
                </div>

                {/* Total caudal */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Total caudal *
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="00"
                      min="0"
                      max="1000"
                      value={caudal}
                      onChange={(e) => setCaudal(e.target.value)}
                      className="flex-1 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    />
                    <select 
                      value={unidadCaudal}
                      onChange={(e) => setUnidadCaudal(e.target.value)}
                      className="w-24 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    >
                      <option value="m3h">m³/h</option>
                      <option value="ls">l/s</option>
                      <option value="gpm">GPM</option>
                      <option value="lmin">l/min</option>
                    </select>
                  </div>
                </div>

                {/* Temperatura */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Temperatura *
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="20"
                      min="-10"
                      max="200"
                      value={temperatura}
                      onChange={(e) => setTemperatura(e.target.value)}
                      className="flex-1 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    />
                    <select 
                      value={unidadTemp}
                      onChange={(e) => setUnidadTemp(e.target.value)}
                      className="w-20 p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    >
                      <option value="C">°C</option>
                      <option value="F">°F</option>
                      <option value="K">K</option>
                    </select>
                  </div>
                </div>

                {/* Fluido bombeado */}
                <div className="space-y-2 md:col-span-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Fluido bombeado
                  </label>
                  <select 
                    value={fluidoBombeado}
                    onChange={(e) => setFluidoBombeado(e.target.value)}
                    className="w-full p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  >
                    <option value="">Seleccionar fluido...</option>
                    {fluidos.map((fluido) => (
                      <option key={fluido.value} value={fluido.value}>
                        {fluido.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Control de velocidad */}
                <div className="space-y-2 md:col-span-2">
                  <label className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Control de velocidad
                  </label>
                  <select 
                    value={controlVelocidad}
                    onChange={(e) => setControlVelocidad(e.target.value)}
                    className="w-full p-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  >
                    <option value="todas">Todas</option>
                    <option value="velocidad_fija">Velocidad fija</option>
                    <option value="velocidad_variable">Velocidad variable</option>
                    <option value="variador_frecuencia">Con variador de frecuencia</option>
                    <option value="control_manual">Control manual</option>
                  </select>
                </div>

              </div>
            </div>
          </div>

          {/* Validación y botón */}
          <div className="mt-8 flex flex-col items-end space-y-3">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              *Obligatorio
            </div>
            
            <motion.button
              onClick={handleMostrarResultados}
              disabled={!isFormValid || isSearching}
              className={`
                px-8 py-3 text-white font-semibold rounded-lg
                transition-all duration-200 flex items-center
                ${isFormValid 
                  ? 'bg-orange-600 hover:bg-orange-700 shadow-lg hover:shadow-xl' 
                  : 'bg-slate-400 cursor-not-allowed'
                }
              `}
              whileHover={isFormValid ? { scale: 1.05 } : {}}
              whileTap={isFormValid ? { scale: 0.95 } : {}}
            >
              {isSearching ? (
                <>
                  <Loader className="animate-spin mr-2" size={20} />
                  Buscando...
                </>
              ) : (
                <>
                  <Search className="mr-2" size={20} />
                  Mostrar resultados
                </>
              )}
            </motion.button>
          </div>

          {/* Error */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
            >
              <p className="text-red-700 dark:text-red-400 text-sm">{error}</p>
            </motion.div>
          )}
        </motion.div>

        {/* Sección de resultados */}
        <AnimatePresence>
          {mostrarResultados && (
            <motion.div
              id="resultados-section"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ delay: 0.3 }}
              className="mt-8 max-w-5xl mx-auto"
            >
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8">
                <div className="flex items-center mb-6">
                  <CheckCircle className="text-green-600 mr-3" size={28} />
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Resultados encontrados ({resultados.length})
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {resultados.map((bomba, index) => (
                    <motion.div
                      key={bomba.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="border border-slate-200 dark:border-slate-600 rounded-xl p-6 hover:shadow-lg transition-shadow"
                    >
                      <div className="flex items-start space-x-4">
                        <img
                          src={bomba.imagen}
                          alt={bomba.modelo}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-1">
                            {bomba.modelo}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                            {bomba.tipo}
                          </p>

                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-slate-600 dark:text-slate-400">Altura máx:</span>
                              <span className="font-medium text-slate-900 dark:text-white">
                                {bomba.altura_max.toFixed(1)} {unidadAltura}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-600 dark:text-slate-400">Caudal máx:</span>
                              <span className="font-medium text-slate-900 dark:text-white">
                                {bomba.caudal_max.toFixed(1)} {unidadCaudal}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-600 dark:text-slate-400">Potencia:</span>
                              <span className="font-medium text-slate-900 dark:text-white">
                                {bomba.potencia}
                              </span>
                            </div>
                          </div>

                          <div className="mt-4 pt-3 border-t border-slate-200 dark:border-slate-600">
                            <div className="flex items-center justify-between">
                              <span className="text-lg font-bold text-green-600">
                                ${bomba.precio.toLocaleString('es-CL')}
                              </span>
                              <motion.button
                                onClick={() => {
                                  const mensaje = `Hola! Me interesa la bomba ${bomba.modelo} (${bomba.tipo}). ¿Pueden darme más información sobre disponibilidad y características técnicas?`;
                                  const whatsappURL = `https://wa.me/56996512719?text=${encodeURIComponent(mensaje)}`;
                                  window.open(whatsappURL, '_blank');
                                }}
                                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center text-sm"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <FaWhatsapp className="mr-1" size={16} />
                                Consultar
                              </motion.button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-8 text-center">
                  <motion.button
                    onClick={() => window.open('https://xzb7d0-uq.myshopify.com/password')}
                    className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ExternalLink className="mr-2" size={20} />
                    Ver más productos en nuestro catálogo
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Sección sin resultados */}
        <AnimatePresence>
          {mostrarSinResultados && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ delay: 0.3 }}
              className="mt-8 max-w-5xl mx-auto"
            >
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-2xl p-8">
                <div className="flex items-center mb-6">
                  <AlertTriangle className="text-yellow-600 dark:text-yellow-400 mr-3" size={28} />
                  <h3 className="text-2xl font-semibold text-yellow-800 dark:text-yellow-200">
                    No encontramos coincidencias exactas
                  </h3>
                </div>

                <p className="text-yellow-700 dark:text-yellow-300 mb-6 text-lg leading-relaxed">
                  Basado en tus especificaciones técnicas, no tenemos productos que coincidan exactamente.
                  Pero nuestros especialistas pueden ayudarte a encontrar la solución perfecta.
                </p>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 mb-6">
                  <h4 className="font-semibold text-slate-900 dark:text-white mb-3">
                    Tus especificaciones:
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div><strong>Aplicación:</strong> {getAplicacionText(selectedApp)}</div>
                    <div><strong>Tipo sistema:</strong> {tipoSistema || 'No especificado'}</div>
                    <div><strong>Altura:</strong> {altura} {unidadAltura}</div>
                    <div><strong>Caudal:</strong> {caudal} {unidadCaudal}</div>
                    <div><strong>Temperatura:</strong> {temperatura}°{unidadTemp}</div>
                    <div><strong>Fluido:</strong> {getFluidoText(fluidoBombeado)}</div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <motion.button
                    onClick={handleConsultarExperto}
                    className="bg-green-600 text-white px-8 py-4 rounded-xl hover:bg-green-700 transition-colors flex items-center justify-center font-semibold"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaWhatsapp className="mr-3" size={20} />
                    Consultar con experto
                  </motion.button>

                  <motion.button
                    onClick={() => window.open('https://xzb7d0-uq.myshopify.com/password')}
                    className="border-2 border-blue-600 text-blue-600 dark:text-blue-400 px-8 py-4 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors flex items-center justify-center font-semibold"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ExternalLink className="mr-3" size={20} />
                    Ver catálogo completo
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};
