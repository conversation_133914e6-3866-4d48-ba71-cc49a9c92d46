import { motion } from 'framer-motion';
import {
  Mail,
  MapPin,
  Monitor,
  Moon,
  Phone,
  Sun
} from 'lucide-react';
import React, { useState } from 'react';
import { FaFacebook, FaInstagram, FaLinkedin } from 'react-icons/fa';

export const Footer: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const currentYear = new Date().getFullYear();

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  };

  const themeIcon = theme === 'light' ? Sun : theme === 'dark' ? Moon : Monitor;
  const ThemeIcon = themeIcon;

  const contactInfo = [
    {
      icon: MapPin,
      title: 'Dirección',
      content: 'Av. Alemania 1234, Temuco, La Araucanía, Chile'
    },
    {
      icon: Phone,
      title: 'Teléfo<PERSON>',
      content: '+56 9 8765 4321'
    },
    {
      icon: Mail,
      title: 'Email',
      content: '<EMAIL>'
    }
  ];

  const quickLinks = [
    { name: 'Inicio', href: '#inicio' },
    { name: 'Productos', href: '#productos' },
    { name: 'Servicios', href: '#servicios' },
    { name: 'Contacto', href: '#contacto' },
    { name: 'Catálogo', href: '#catalogo' },
    { name: 'Proyectos', href: '#proyectos' }
  ];

  const socialLinks = [
    { icon: FaFacebook, href: 'https://facebook.com/hidroimplementos', label: 'Facebook' },
    { icon: FaInstagram, href: 'https://instagram.com/hidroimplementos', label: 'Instagram' },
    { icon: FaLinkedin, href: 'https://linkedin.com/company/hidroimplementos', label: 'LinkedIn' }
  ];

  return (
    <footer id="contacto" className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-t border-slate-200/20 dark:border-slate-700/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <motion.div
              className="flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
            >
              <img
                src="/logos/logo_hidroimplementos.png"
                alt="Hidroimplementos SPA Logo"
                className="h-10 w-auto object-contain"
              />
              <span className="text-xl font-bold text-gradient-hidro">
                Hidroimplementos SPA
              </span>
            </motion.div>
            <p className="text-slate-600 dark:text-slate-300">
              Especialistas en sistemas de riego tecnificado, bombas industriales y soluciones hídricas para La Araucanía y el sur de Chile. Más de 15 años de experiencia.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-500 dark:text-slate-400">Tema:</span>
              <motion.button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:text-hidro-blue-600 dark:hover:text-hidro-blue-400 transition-colors cursor-pointer"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ThemeIcon className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Contacto
            </h3>
            {contactInfo.map((item, index) => (
              <motion.div 
                key={index}
                className="flex items-start space-x-3"
                whileHover={{ x: 5 }}
              >
                <item.icon className="w-5 h-5 text-hidro-blue-600 dark:text-hidro-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-slate-900 dark:text-white">
                    {item.title}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    {item.content}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Enlaces Rápidos
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {quickLinks.map((link, index) => (
                <motion.a
                  key={index}
                  href={link.href}
                  className="text-slate-600 dark:text-slate-300 hover:text-hidro-blue-600 dark:hover:text-hidro-blue-400 transition-colors text-sm cursor-pointer"
                  whileHover={{ x: 5 }}
                >
                  {link.name}
                </motion.a>
              ))}
            </div>
          </div>

          {/* Social Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Síguenos
            </h3>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  className="w-10 h-10 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center text-slate-600 dark:text-slate-400 hover:text-white hover:bg-hidro-blue-600 transition-all cursor-pointer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
            <div className="mt-6">
              <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                Horarios de atención:
              </p>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                Lun - Vie: 8:00 - 18:00<br />
                Sáb: 9:00 - 14:00
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-slate-200 dark:border-slate-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              © {currentYear} Hidroimplementos SPA. Todos los derechos reservados.
            </p>
            <div className="flex items-center space-x-6 text-sm text-slate-500 dark:text-slate-400">
              <a href="#" className="hover:text-hidro-blue-600 dark:hover:text-hidro-blue-400 transition-colors cursor-pointer">
                Política de Privacidad
              </a>
              <a href="#" className="hover:text-hidro-blue-600 dark:hover:text-hidro-blue-400 transition-colors cursor-pointer">
                Términos de Servicio
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};