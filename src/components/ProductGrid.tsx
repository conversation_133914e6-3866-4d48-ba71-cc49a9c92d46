import { motion } from 'framer-motion';
import { Droplets, Gauge, Wrench, Zap } from 'lucide-react';
import React from 'react';

const products = [
  {
    id: 1,
    name: 'Bomba Centrífuga KSB',
    image: '/bomba-centrifuga-ksb.webp',
    category: 'Bombas',
    description: 'Bomba centrífuga de alta eficiencia para aplicaciones industriales',
    icon: Gauge
  },
  {
    id: 2,
    name: 'Bomba Sumergible Grundfos',
    image: '/bomba-sumergible-grundfos.webp',
    category: 'Bombas',
    description: 'Bomba sumergible para pozos profundos y aplicaciones residenciales',
    icon: Droplets
  },
  {
    id: 3,
    name: 'Sistema de Goteo Premium',
    image: '/sistema-goteo-premium.webp',
    category: 'Riego',
    description: 'Sistema de riego por goteo de alta precisión para cultivos',
    icon: Droplets
  },
  {
    id: 4,
    name: 'Aspersor Rotativo',
    image: '/aspersor-rotativo.webp',
    category: '<PERSON>ie<PERSON>',
    description: 'Aspersor rotativo de largo alcance para grandes superficies',
    icon: Zap
  },
  {
    id: 5,
    name: '<PERSON><PERSON>l<PERSON><PERSON> Solenoide Hunter',
    image: '/valvula-solenoide-hunter.webp',
    category: 'Válvulas',
    description: 'Válvula solenoide de alta durabilidad para automatización',
    icon: Wrench
  }
];

export const ProductGrid: React.FC = () => {
  return (
    <section id="productos" className="py-20 bg-slate-50 dark:bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Nuestros Productos
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Soluciones completas en sistemas de riego y bombeo para todas tus necesidades
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5 }}
              className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg overflow-hidden group"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {product.category}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-lg flex items-center justify-center">
                    <product.icon className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                    {product.name}
                  </h3>
                </div>

                <p className="text-slate-600 dark:text-slate-300 mb-4">
                  {product.description}
                </p>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full bg-gradient-to-r from-blue-600 to-cyan-500 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all"
                >
                  Ver Detalles
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};