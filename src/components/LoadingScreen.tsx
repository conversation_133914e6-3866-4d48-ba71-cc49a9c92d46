import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaWater, FaTools, FaCog } from 'react-icons/fa';
import { IoWaterOutline } from 'react-icons/io5';

interface LoadingScreenProps {
  isLoading: boolean;
  progress: number;
  onComplete?: () => void;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  isLoading,
  progress,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [displayProgress, setDisplayProgress] = useState(0);

  const loadingSteps = [
    { icon: FaWater, text: 'Cargando sistemas de riego...', color: 'text-blue-500' },
    { icon: FaTools, text: 'Preparando herramientas...', color: 'text-green-500' },
    { icon: FaCog, text: 'Optimizando experiencia...', color: 'text-purple-500' },
    { icon: IoWaterOutline, text: 'Finalizando carga...', color: 'text-cyan-500' }
  ];

  useEffect(() => {
    const stepProgress = Math.floor(progress / 25);
    setCurrentStep(Math.min(stepProgress, loadingSteps.length - 1));
    
    // Smooth progress animation
    const timer = setTimeout(() => {
      setDisplayProgress(progress);
    }, 100);

    return () => clearTimeout(timer);
  }, [progress, loadingSteps.length]);

  useEffect(() => {
    if (progress >= 100 && onComplete) {
      const timer = setTimeout(onComplete, 500);
      return () => clearTimeout(timer);
    }
  }, [progress, onComplete]);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.5 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-cyan-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"
        >
          <div className="text-center space-y-8 max-w-md mx-auto px-6">
            {/* Logo Animation */}
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="relative"
            >
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-2xl">
                <FaWater className="w-12 h-12 text-white" />
              </div>
              
              {/* Ripple Effect */}
              <motion.div
                className="absolute inset-0 w-24 h-24 mx-auto rounded-full border-2 border-blue-400"
                animate={{
                  scale: [1, 1.5, 2],
                  opacity: [0.8, 0.4, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              />
            </motion.div>

            {/* Company Name */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <h1 className="text-3xl font-bold text-slate-800 dark:text-white mb-2">
                Hidroimplementos
              </h1>
              <p className="text-slate-600 dark:text-slate-300 text-lg">
                Tecnología de Riego Avanzada
              </p>
            </motion.div>

            {/* Loading Step */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-center space-x-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  {React.createElement(loadingSteps[currentStep].icon, {
                    className: `w-6 h-6 ${loadingSteps[currentStep].color}`
                  })}
                </motion.div>
                <span className="text-slate-700 dark:text-slate-300 font-medium">
                  {loadingSteps[currentStep].text}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${displayProgress}%` }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                />
              </div>

              {/* Progress Percentage */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="text-sm text-slate-500 dark:text-slate-400 font-medium"
              >
                {Math.round(displayProgress)}%
              </motion.div>
            </motion.div>

            {/* Floating Particles */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-blue-400 rounded-full opacity-30"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${30 + (i % 2) * 40}%`
                  }}
                  animate={{
                    y: [-20, -40, -20],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.5,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
