import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Loader, ZoomIn, ZoomOut, RotateCw, Download } from 'lucide-react';

interface ImageOptimizerProps {
  src: string;
  alt: string;
  className?: string;
  enableZoom?: boolean;
  enableFilters?: boolean;
  lazy?: boolean;
}

export const ImageOptimizer: React.FC<ImageOptimizerProps> = ({
  src,
  alt,
  className = '',
  enableZoom = false,
  enableFilters = true,
  lazy = true
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [filter, setFilter] = useState('none');
  const imgRef = useRef<HTMLImageElement>(null);
  const [inView, setInView] = useState(!lazy);

  useEffect(() => {
    if (!lazy) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [lazy]);

  const filters = {
    none: '',
    enhance: 'contrast(1.1) brightness(1.05) saturate(1.1)',
    vivid: 'contrast(1.2) brightness(1.1) saturate(1.3)',
    sharp: 'contrast(1.15) brightness(1.02) saturate(1.05) blur(0px)',
    warm: 'contrast(1.1) brightness(1.08) saturate(1.2) hue-rotate(5deg)',
    cool: 'contrast(1.1) brightness(1.05) saturate(1.1) hue-rotate(-5deg)',
    dramatic: 'contrast(1.3) brightness(0.95) saturate(1.4)'
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Loading Skeleton */}
      {!isLoaded && inView && (
        <div className="absolute inset-0 bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 dark:from-slate-700 dark:via-slate-600 dark:to-slate-700 animate-pulse">
          <div className="flex items-center justify-center h-full">
            <Loader className="w-8 h-8 animate-spin text-slate-400" />
          </div>
        </div>
      )}

      {/* Main Image */}
      {inView && (
        <motion.img
          ref={imgRef}
          src={src}
          alt={alt}
          className={`
            w-full h-full object-cover transition-all duration-500
            ${isLoaded ? 'opacity-100' : 'opacity-0'}
            ${isZoomed ? 'scale-150 cursor-zoom-out' : 'cursor-zoom-in'}
          `}
          style={{
            filter: enableFilters ? filters[filter as keyof typeof filters] : 'none'
          }}
          onLoad={() => setIsLoaded(true)}
          onError={() => setIsError(true)}
          onClick={() => enableZoom && setIsZoomed(!isZoomed)}
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          whileHover={enableZoom ? { scale: 1.05 } : {}}
        />
      )}

      {/* Filter Controls */}
      {enableFilters && isLoaded && (
        <div className="absolute top-2 right-2 bg-black/50 backdrop-blur-sm rounded-lg p-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="bg-transparent text-white text-xs border-none outline-none"
          >
            <option value="none" className="text-black">Original</option>
            <option value="enhance" className="text-black">Mejorado</option>
            <option value="vivid" className="text-black">Vívido</option>
            <option value="sharp" className="text-black">Nítido</option>
            <option value="warm" className="text-black">Cálido</option>
            <option value="cool" className="text-black">Frío</option>
            <option value="dramatic" className="text-black">Dramático</option>
          </select>
        </div>
      )}

      {/* Zoom Controls */}
      {enableZoom && isLoaded && (
        <div className="absolute bottom-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsZoomed(!isZoomed);
            }}
            className="p-2 bg-black/50 backdrop-blur-sm rounded-lg text-white hover:bg-black/70 transition-colors"
          >
            {isZoomed ? <ZoomOut className="w-4 h-4" /> : <ZoomIn className="w-4 h-4" />}
          </button>
        </div>
      )}

      {/* Error State */}
      {isError && (
        <div className="absolute inset-0 bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
          <div className="text-center text-slate-500 dark:text-slate-400">
            <div className="w-12 h-12 bg-slate-300 dark:bg-slate-600 rounded-lg mx-auto mb-2"></div>
            <p className="text-sm">Error al cargar imagen</p>
          </div>
        </div>
      )}
    </div>
  );
};