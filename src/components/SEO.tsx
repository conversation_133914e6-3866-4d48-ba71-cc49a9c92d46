import React from 'react';
import { Helmet } from 'react-helmet';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
}

export const SEO: React.FC<SEOProps> = ({
  title = 'Hidroimplementos SPA - Tecnología de Riego Avanzada para Chile',
  description = 'Líderes en soluciones de riego, bombas industriales y sistemas automatizados en La Araucanía y sur de Chile. 25 años de experiencia en tecnología de riego.',
  keywords = 'bombas de agua, riego, sistemas de riego, bombas industriales, aspersión, goteo, automatización, La Araucanía, Chile',
  image = 'https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1200',
  url = 'https://hidroimplementos.cl'
}) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Organization",
        "@id": `${url}/#organization`,
        "name": "Hidroimplementos SPA",
        "url": url,
        "logo": {
          "@type": "ImageObject",
          "url": `${url}/logo.png`
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+56-9-1234-5678",
          "contactType": "customer service",
          "areaServed": "CL",
          "availableLanguage": "Spanish"
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Av. Alemania 1234",
          "addressLocality": "Temuco",
          "addressRegion": "La Araucanía",
          "addressCountry": "CL"
        }
      },
      {
        "@type": "LocalBusiness",
        "@id": `${url}/#business`,
        "name": "Hidroimplementos SPA",
        "description": description,
        "url": url,
        "image": image,
        "priceRange": "$$",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Av. Alemania 1234",
          "addressLocality": "Temuco",
          "addressRegion": "La Araucanía",
          "postalCode": "4780000",
          "addressCountry": "CL"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": "-38.7369",
          "longitude": "-72.5904"
        },
        "openingHoursSpecification": [
          {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            "opens": "08:00",
            "closes": "18:00"
          },
          {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": "Saturday",
            "opens": "09:00",
            "closes": "14:00"
          }
        ]
      }
    ]
  };

  return (
    <Helmet>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="Spanish" />
      <meta name="author" content="Hidroimplementos SPA" />
      
      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Hidroimplementos SPA" />
      <meta property="og:locale" content="es_CL" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Favicon */}
      <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
      <link rel="icon" type="image/png" href="/favicon.png" />
      
      {/* Viewport */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#0066FF" />
      <meta name="msapplication-TileColor" content="#0066FF" />
    </Helmet>
  );
};