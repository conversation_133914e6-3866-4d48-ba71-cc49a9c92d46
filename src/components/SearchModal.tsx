import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, MessageCircle, Filter } from 'lucide-react';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    application: '',
    height: '',
    flow: '',
    temperature: ''
  });

  const applications = ['Riego', 'Industrial', 'Doméstico', 'Piscina'];
  
  const handleWhatsAppConsult = () => {
    const message = `Hola! Busco productos con las siguientes características:
${searchTerm ? `Búsqueda: ${searchTerm}` : ''}
${filters.application ? `Aplicación: ${filters.application}` : ''}
${filters.height ? `Altura: ${filters.height}m` : ''}
${filters.flow ? `Caudal: ${filters.flow}m³/h` : ''}
${filters.temperature ? `Temperatura: ${filters.temperature}°C` : ''}

¿Podrían asesorarme? 🔍`;
    
    window.open(`https://wa.me/56912345678?text=${encodeURIComponent(message)}`, '_blank');
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-lg flex items-center justify-center">
                  <Search className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-slate-900 dark:text-white">
                  Encuentra tu Bomba Ideal
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
              >
                <X className="w-5 h-5 text-slate-500" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Buscar por modelo, marca o tipo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                />
              </div>

              {/* Filters */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 text-slate-700 dark:text-slate-300">
                  <Filter className="w-4 h-4" />
                  <span className="font-medium">Filtros Avanzados</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Application */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      🏭 Aplicación
                    </label>
                    <select
                      value={filters.application}
                      onChange={(e) => setFilters({...filters, application: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Seleccionar...</option>
                      {applications.map(app => (
                        <option key={app} value={app}>{app}</option>
                      ))}
                    </select>
                  </div>

                  {/* Height */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      📏 Altura (m)
                    </label>
                    <input
                      type="number"
                      placeholder="Ej: 50"
                      value={filters.height}
                      onChange={(e) => setFilters({...filters, height: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Flow */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      💧 Caudal (m³/h)
                    </label>
                    <input
                      type="number"
                      placeholder="Ej: 100"
                      value={filters.flow}
                      onChange={(e) => setFilters({...filters, flow: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Temperature */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      🌡️ Temperatura (°C)
                    </label>
                    <input
                      type="number"
                      placeholder="Ej: 60"
                      value={filters.temperature}
                      onChange={(e) => setFilters({...filters, temperature: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-500 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all flex items-center justify-center space-x-2"
                >
                  <Search className="w-5 h-5" />
                  <span>Buscar Productos</span>
                </motion.button>
                
                <motion.button
                  onClick={handleWhatsAppConsult}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-all flex items-center justify-center space-x-2"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>Consultar Experto</span>
                </motion.button>
              </div>

              {/* Help Text */}
              <p className="text-sm text-slate-500 dark:text-slate-400 text-center">
                ¿No encuentras lo que buscas? Nuestros expertos te ayudarán a encontrar la solución perfecta.
              </p>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};