import React from 'react';
import { motion } from 'framer-motion';
import { Target, Wrench, MapPin, Lightbulb } from 'lucide-react';

interface ValueItem {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  highlight: string;
}

const values: ValueItem[] = [
  {
    icon: Target,
    title: 'Precisión Técnica',
    description: '25 años de experiencia en soluciones de riego industrial',
    highlight: '25 años'
  },
  {
    icon: Wrench,
    title: 'Soporte Integral',
    description: 'Instalación, mantención y soporte técnico completo',
    highlight: 'Soporte 24/7'
  },
  {
    icon: MapPin,
    title: 'Cobertura Regional',
    description: 'Servicio especializado en La Araucanía y sur de Chile',
    highlight: 'Cobertura Total'
  },
  {
    icon: Lightbulb,
    title: 'Innovación Constante',
    description: 'Últimas tecnologías en automatización y eficiencia',
    highlight: 'Tecnología Avanzada'
  }
];

export const CompanyValues: React.FC = () => {
  return (
    <section className="py-20 bg-white dark:bg-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            ¿Por qué elegirnos?
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Compromiso con la excelencia y resultados que transforman tus proyectos
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5 }}
              className="text-center group"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <value.icon className="w-10 h-10 text-white" />
              </div>
              
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">
                {value.title}
              </h3>
              
              <p className="text-slate-600 dark:text-slate-300 mb-3">
                {value.description}
              </p>
              
              <div className="inline-block bg-gradient-to-r from-blue-600 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                {value.highlight}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};