import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Target, Droplets, Factory, Home, ArrowDown, ArrowRight, 
  CheckCircle, Settings, ArrowUp, Thermometer, Beaker, 
  Gauge, Search, Loader, AlertTriangle, MessageCircle
} from 'lucide-react';

interface Aplicacion {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
}

interface Bomba {
  id: string;
  nombre: string;
  imagen: string;
  caudal_max: number;
  altura_max: number;
  precio: number;
  sku: string;
}

const aplicaciones: Aplicacion[] = [
  {
    id: 'riego',
    label: 'Riego agrícola/jardínería',
    icon: <Droplets className="text-green-600" size={20} />,
    description: 'Cultivos, jardines, invernaderos'
  },
  {
    id: 'industrial', 
    label: 'Uso industrial/comercial',
    icon: <Factory className="text-blue-600" size={20} />,
    description: 'Procesos, enfriamiento, limpieza'
  },
  {
    id: 'domestico',
    label: 'Uso doméstico/residencial', 
    icon: <Home className="text-purple-600" size={20} />,
    description: 'Casa, piscina, presión agua'
  },
  {
    id: 'achique',
    label: 'Achique/drenaje',
    icon: <ArrowDown className="text-red-600" size={20} />,
    description: 'Evacuar agua, pozos, sótanos'
  }
];

// Simulación de base de datos de bombas
const bombasDB: Bomba[] = [
  {
    id: '1',
    nombre: 'Bomba Centrífuga Industrial KSB Etanorm',
    imagen: 'https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=400',
    caudal_max: 150,
    altura_max: 80,
    precio: 850000,
    sku: 'KSB-ETN-150'
  },
  {
    id: '2',
    nombre: 'Bomba Sumergible Grundfos SP',
    imagen: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=400',
    caudal_max: 100,
    altura_max: 120,
    precio: 650000,
    sku: 'GRU-SP-100'
  },
  {
    id: '3',
    nombre: 'Bomba Autoaspirante Pedrollo JSW',
    imagen: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=400',
    caudal_max: 80,
    altura_max: 50,
    precio: 320000,
    sku: 'PED-JSW-80'
  }
];

export const SmartSearch: React.FC = () => {
  const [selectedApp, setSelectedApp] = useState<string>('');
  const [tipoSistema, setTipoSistema] = useState<string>('');
  const [altura, setAltura] = useState<string>('');
  const [caudal, setCaudal] = useState<string>('');
  const [temperatura, setTemperatura] = useState<string>('20');
  const [fluido, setFluido] = useState<string>('agua_limpia');
  const [controlVelocidad, setControlVelocidad] = useState<string>('todas');
  const [unidadAltura, setUnidadAltura] = useState<string>('m');
  const [unidadCaudal, setUnidadCaudal] = useState<string>('m3h');
  const [unidadTemp, setUnidadTemp] = useState<string>('C');
  
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [showNoResults, setShowNoResults] = useState<boolean>(false);
  const [resultados, setResultados] = useState<Bomba[]>([]);

  const isFormValid = selectedApp && tipoSistema && altura && caudal;

  const buscarBombas = async (params: any): Promise<Bomba[]> => {
    // Simulación de búsqueda en base de datos
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const alturaNum = parseFloat(params.altura_min);
    const caudalNum = parseFloat(params.caudal_min);
    
    return bombasDB.filter(bomba => 
      bomba.altura_max >= alturaNum && 
      bomba.caudal_max >= caudalNum
    );
  };

  const handleSearch = async () => {
    setIsSearching(true);
    setShowResults(false);
    setShowNoResults(false);
    
    const searchParams = {
      aplicacion: selectedApp,
      tipo_sistema: tipoSistema,
      altura_min: parseFloat(altura) * 0.8,
      altura_max: parseFloat(altura) * 1.2,
      caudal_min: parseFloat(caudal) * 0.8,
      caudal_max: parseFloat(caudal) * 1.2,
      temperatura,
      fluido,
      control_velocidad: controlVelocidad
    };
    
    try {
      const resultados = await buscarBombas(searchParams);
      
      if (resultados.length > 0) {
        setResultados(resultados);
        setShowResults(true);
      } else {
        setShowNoResults(true);
      }
    } catch (error) {
      console.error('Error en búsqueda:', error);
      setShowNoResults(true);
    } finally {
      setIsSearching(false);
    }
  };

  const handleWhatsAppConsult = () => {
    const mensaje = `Hola! Busqué una bomba con estas especificaciones:
- Aplicación: ${aplicaciones.find(a => a.id === selectedApp)?.label || 'No especificada'}
- Tipo: ${tipoSistema}
- Altura: ${altura}${unidadAltura}  
- Caudal: ${caudal}${unidadCaudal === 'm3h' ? 'm³/h' : unidadCaudal}
- Temperatura: ${temperatura}°${unidadTemp}
- Fluido: ${fluido.replace('_', ' ')}

¿Pueden ayudarme a encontrar la bomba ideal? 🔍`;
    
    const whatsappURL = `https://wa.me/56930518083?text=${encodeURIComponent(mensaje)}`;
    window.open(whatsappURL, '_blank');
  };

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-cyan-50 to-green-50 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Título Principal */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            💧 ¿Cómo quieres que sea tu bomba?
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            Encuentra la solución perfecta para tu proyecto
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-slate-200/20 dark:border-slate-700/20"
        >
          {/* Sección Aplicación */}
          <div className="mb-8">
            <h3 className="text-2xl font-semibold mb-6 flex items-center text-slate-900 dark:text-white">
              <Target className="mr-3 text-blue-600" size={28} />
              Elige tu aplicación
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {aplicaciones.map((app) => (
                <motion.label 
                  key={app.id} 
                  className={`
                    flex items-center p-6 rounded-xl border-2 cursor-pointer
                    transition-all duration-300 hover:shadow-lg
                    ${selectedApp === app.id 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg' 
                      : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <input 
                    type="radio" 
                    name="aplicacion"
                    value={app.id}
                    className="sr-only"
                    onChange={(e) => setSelectedApp(e.target.value)}
                  />
                  <div className="flex items-center space-x-4 flex-1">
                    {app.icon}
                    <div>
                      <div className="font-semibold text-slate-900 dark:text-white">{app.label}</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">{app.description}</div>
                    </div>
                  </div>
                  {selectedApp === app.id && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    >
                      <CheckCircle className="text-blue-600" size={24} />
                    </motion.div>
                  )}
                </motion.label>
              ))}
            </div>

            {/* Link Catálogo */}
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="mt-6 p-4 bg-slate-50 dark:bg-slate-700 rounded-xl"
            >
              <div className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                <ArrowRight className="mr-2" size={16} />
                <span className="font-medium">¿No has encontrado tu aplicación?</span>
              </div>
              <button 
                onClick={() => window.open('https://xzb7d0-uq.myshopify.com/password')}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium mt-1 transition-colors"
              >
                Descubre nuestro catálogo completo
              </button>
            </motion.div>
          </div>

          {/* Formulario Técnico */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Tipo de sistema */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <Settings className="mr-2 text-blue-600" size={18} />
                Tipo de sistema *
              </label>
              <select 
                value={tipoSistema}
                onChange={(e) => setTipoSistema(e.target.value)}
                className="w-full p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
              >
                <option value="">Selecciona...</option>
                <option value="centrifuga">Bomba centrífuga</option>
                <option value="sumergible">Bomba sumergible</option>
                <option value="autoaspirante">Bomba autoaspirante</option>
                <option value="presion">Grupo de presión</option>
                <option value="dosificadora">Bomba dosificadora</option>
              </select>
            </div>

            {/* Altura total */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <ArrowUp className="mr-2 text-green-600" size={18} />
                Altura total *
              </label>
              <div className="flex space-x-2">
                <input 
                  type="number" 
                  placeholder="0"
                  min="0" 
                  max="500"
                  value={altura}
                  onChange={(e) => setAltura(e.target.value)}
                  className="flex-1 p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                />
                <select 
                  value={unidadAltura}
                  onChange={(e) => setUnidadAltura(e.target.value)}
                  className="w-20 p-4 border border-slate-200 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                >
                  <option value="m">m</option>
                  <option value="ft">ft</option>
                </select>
              </div>
              <div className="text-xs text-slate-500 dark:text-slate-400">
                Altura desde succión hasta descarga
              </div>
            </div>

            {/* Caudal total */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <Droplets className="mr-2 text-blue-600" size={18} />
                Caudal total *
              </label>
              <div className="flex space-x-2">
                <input 
                  type="number" 
                  placeholder="0"
                  min="0" 
                  max="1000"
                  value={caudal}
                  onChange={(e) => setCaudal(e.target.value)}
                  className="flex-1 p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                />
                <select 
                  value={unidadCaudal}
                  onChange={(e) => setUnidadCaudal(e.target.value)}
                  className="w-24 p-4 border border-slate-200 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                >
                  <option value="m3h">m³/h</option>
                  <option value="lmin">L/min</option>
                  <option value="gpm">GPM</option>
                </select>
              </div>
              <div className="text-xs text-slate-500 dark:text-slate-400">
                Volumen de agua por hora
              </div>
            </div>

            {/* Temperatura */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <Thermometer className="mr-2 text-red-600" size={18} />
                Temperatura *
              </label>
              <div className="flex space-x-2">
                <input 
                  type="number" 
                  placeholder="20"
                  min="-10" 
                  max="100"
                  value={temperatura}
                  onChange={(e) => setTemperatura(e.target.value)}
                  className="flex-1 p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                />
                <select 
                  value={unidadTemp}
                  onChange={(e) => setUnidadTemp(e.target.value)}
                  className="w-20 p-4 border border-slate-200 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                >
                  <option value="C">°C</option>
                  <option value="F">°F</option>
                </select>
              </div>
            </div>

            {/* Fluido bombeado */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <Beaker className="mr-2 text-purple-600" size={18} />
                Fluido bombeado
              </label>
              <select 
                value={fluido}
                onChange={(e) => setFluido(e.target.value)}
                className="w-full p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
              >
                <option value="agua_limpia">Agua limpia</option>
                <option value="agua_turbia">Agua con sedimentos</option>
                <option value="agua_residual">Agua residual</option>
                <option value="quimicos">Productos químicos</option>
                <option value="aceites">Aceites/combustibles</option>
              </select>
            </div>

            {/* Control de velocidad */}
            <div className="space-y-3">
              <label className="flex items-center text-sm font-semibold text-slate-700 dark:text-slate-300">
                <Gauge className="mr-2 text-orange-600" size={18} />
                Control de velocidad
              </label>
              <select 
                value={controlVelocidad}
                onChange={(e) => setControlVelocidad(e.target.value)}
                className="w-full p-4 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
              >
                <option value="todas">Todas</option>
                <option value="variador">Con variador de frecuencia</option>
                <option value="fija">Velocidad fija</option>
                <option value="manual">Control manual</option>
              </select>
            </div>
          </div>

          {/* Botón Buscar */}
          <div className="text-center mb-8">
            <motion.button 
              onClick={handleSearch}
              disabled={!isFormValid || isSearching}
              className={`
                px-12 py-4 text-lg font-bold rounded-xl
                transition-all duration-300 transform hover:scale-105
                ${isFormValid 
                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-xl hover:shadow-2xl hover:from-orange-600 hover:to-red-600' 
                  : 'bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed'
                }
              `}
              whileHover={isFormValid ? { scale: 1.05 } : {}}
              whileTap={isFormValid ? { scale: 0.95 } : {}}
            >
              {isSearching ? (
                <div className="flex items-center">
                  <Loader className="animate-spin mr-3" size={24} />
                  Buscando...
                </div>
              ) : (
                <div className="flex items-center">
                  <Search className="mr-3" size={24} />
                  Mostrar resultados
                </div>
              )}
            </motion.button>
            
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-3">
              * Campos obligatorios
            </p>
          </div>

          {/* Resultados */}
          <AnimatePresence>
            {showResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="border-t border-slate-200 dark:border-slate-600 pt-8"
              >
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 text-center">
                  🎯 Encontramos {resultados.length} bomba{resultados.length !== 1 ? 's' : ''} para ti
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {resultados.map((bomba) => (
                    <motion.div 
                      key={bomba.id} 
                      className="border border-slate-200 dark:border-slate-600 rounded-xl p-6 hover:shadow-xl transition-all duration-300 bg-white dark:bg-slate-700"
                      whileHover={{ y: -5 }}
                    >
                      <img 
                        src={bomba.imagen} 
                        alt={bomba.nombre} 
                        className="w-full h-48 object-cover rounded-lg mb-4" 
                      />
                      <h4 className="font-bold text-lg mb-3 text-slate-900 dark:text-white">{bomba.nombre}</h4>
                      <div className="text-sm text-slate-600 dark:text-slate-300 space-y-2 mb-4">
                        <p className="flex items-center">
                          <Droplets className="w-4 h-4 mr-2 text-blue-600" />
                          Caudal: {bomba.caudal_max} m³/h
                        </p>
                        <p className="flex items-center">
                          <ArrowUp className="w-4 h-4 mr-2 text-green-600" />
                          Altura: {bomba.altura_max} m
                        </p>
                        <p className="flex items-center font-bold text-lg text-blue-600 dark:text-blue-400">
                          💰 Desde: ${bomba.precio.toLocaleString()}
                        </p>
                      </div>
                      <button 
                        onClick={() => window.open(`https://xzb7d0-uq.myshopify.com/password?producto=${bomba.sku}`)}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors"
                      >
                        Ver en tienda
                      </button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {showNoResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="border-t border-slate-200 dark:border-slate-600 pt-8"
              >
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-xl p-8 text-center">
                  <div className="flex items-center justify-center mb-6">
                    <AlertTriangle className="text-yellow-600 dark:text-yellow-400 mr-3" size={32} />
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white">No encontramos coincidencias exactas</h3>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-6 text-lg">
                    Pero nuestros expertos pueden ayudarte a encontrar la bomba perfecta para tu proyecto.
                  </p>
                  <motion.button 
                    onClick={handleWhatsAppConsult}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all flex items-center mx-auto"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <MessageCircle className="mr-3" size={24} />
                    Consultar con experto vía WhatsApp
                  </motion.button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </section>
  );
};