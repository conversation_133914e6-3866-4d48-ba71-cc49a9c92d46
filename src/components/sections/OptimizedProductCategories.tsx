import { motion } from 'framer-motion';
import { Building, ChevronRight, Droplets, Settings, SprayCan as <PERSON><PERSON><PERSON>, <PERSON>p<PERSON>, <PERSON>, Wrench } from 'lucide-react';
import React from 'react';
import { ImageOptimizer } from '../ImageOptimizer';

interface ProductCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
  gradient: string;
  image: string;
  features: string[];
  rating: number;
}

const categories: ProductCategory[] = [
  {
    id: 'bombas',
    name: 'Bombas de Agua',
    icon: Droplets,
    description: 'Bombas centrífugas, sumergibles e industriales de alta eficiencia',
    color: 'blue',
    gradient: 'from-blue-500 to-cyan-500',
    image: '/bomba-centrifuga-ksb.webp',
    features: ['Alta eficiencia', 'Bajo mantenimiento', 'Garantía extendida'],
    rating: 4.8
  },
  {
    id: 'goteo',
    name: '<PERSON><PERSON><PERSON>',
    icon: Sprout,
    description: '<PERSON>ste<PERSON> de riego por goteo eficientes y automatizados',
    color: 'green',
    gradient: 'from-green-500 to-emerald-500',
    image: '/sistema-goteo-premium.webp',
    features: ['Ahorro de agua', 'Automatización', 'Fácil instalación'],
    rating: 4.9
  },
  {
    id: 'aspersion',
    name: 'Aspersión',
    icon: Spray,
    description: 'Aspersores y sistemas de riego por aspersión profesionales',
    color: 'cyan',
    gradient: 'from-cyan-500 to-teal-500',
    image: '/aspersor-rotativo.webp',
    features: ['Cobertura uniforme', 'Ajuste preciso', 'Durabilidad'],
    rating: 4.7
  },
  {
    id: 'automatizacion',
    name: 'Automatización',
    icon: Settings,
    description: 'Controladores inteligentes y sistemas automatizados',
    color: 'purple',
    gradient: 'from-purple-500 to-indigo-500',
    image: 'https://images.pexels.com/photos/1108098/pexels-photo-1108098.jpeg?auto=compress&cs=tinysrgb&w=600',
    features: ['Control remoto', 'Sensores IoT', 'Programación avanzada'],
    rating: 4.9
  },
  {
    id: 'accesorios',
    name: 'Accesorios',
    icon: Wrench,
    description: 'Válvulas, conexiones y accesorios especializados',
    color: 'orange',
    gradient: 'from-orange-500 to-red-500',
    image: 'https://images.pexels.com/photos/1108097/pexels-photo-1108097.jpeg?auto=compress&cs=tinysrgb&w=600',
    features: ['Calidad premium', 'Compatibilidad', 'Resistencia'],
    rating: 4.6
  },
  {
    id: 'proyectos',
    name: 'Proyectos Custom',
    icon: Building,
    description: 'Diseño e implementación de proyectos a medida',
    color: 'slate',
    gradient: 'from-slate-500 to-gray-500',
    image: '/bomba-sumergible-grundfos.webp',
    features: ['Diseño personalizado', 'Asesoría técnica', 'Soporte completo'],
    rating: 5.0
  }
];

export const OptimizedProductCategories: React.FC = () => {
  const handleCategoryClick = (categoryId: string, categoryName: string) => {
    const message = `Hola! Me interesa la categoría "${categoryName}" que estaba viendo 📦`;
    window.open(`https://wa.me/56912345678?text=${encodeURIComponent(message)}`, '_blank');
  };

  return (
    <section id="productos" className="py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Nuestros Productos
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Soluciones completas para sistemas de riego, bombeo e irrigación industrial
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-lg rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group border border-slate-200/20 dark:border-slate-700/20"
              onClick={() => handleCategoryClick(category.id, category.name)}
            >
              {/* Image Header */}
              <div className="relative h-48 overflow-hidden">
                <ImageOptimizer
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  enableFilters={true}
                />
                
                {/* Gradient Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-t ${category.gradient} opacity-20 group-hover:opacity-30 transition-opacity`} />
                
                {/* Icon Badge */}
                <div className={`absolute top-4 left-4 w-12 h-12 bg-gradient-to-r ${category.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>

                {/* Rating Badge */}
                <div className="absolute top-4 right-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-semibold text-slate-900 dark:text-white">
                    {category.rating}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {category.name}
                </h3>
                
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  {category.description}
                </p>

                {/* Features */}
                <div className="space-y-2 mb-6">
                  {category.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                      <div className={`w-2 h-2 bg-gradient-to-r ${category.gradient} rounded-full mr-3`} />
                      {feature}
                    </div>
                  ))}
                </div>
                
                {/* CTA Button */}
                <motion.div 
                  className="flex items-center justify-between"
                  whileHover={{ x: 5 }}
                >
                  <span className="text-blue-600 dark:text-blue-400 font-semibold group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Ver productos
                  </span>
                  <div className={`w-8 h-8 bg-gradient-to-r ${category.gradient} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    <ChevronRight className="w-4 h-4 text-white" />
                  </div>
                </motion.div>
              </div>

              {/* Hover Effect Border */}
              <div className={`absolute inset-0 border-2 border-transparent group-hover:border-gradient-to-r group-hover:${category.gradient} rounded-3xl transition-all pointer-events-none`} />
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-cyan-500 rounded-3xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              ¿No encuentras lo que buscas?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Nuestro equipo de expertos puede ayudarte a encontrar la solución perfecta para tu proyecto específico.
            </p>
            <motion.button
              onClick={() => {
                const message = "Hola! Necesito asesoría para encontrar la solución perfecta para mi proyecto 🔧";
                window.open(`https://wa.me/56912345678?text=${encodeURIComponent(message)}`, '_blank');
              }}
              className="bg-white text-blue-600 px-8 py-3 rounded-xl font-semibold hover:bg-blue-50 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Consultar Experto
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};