import { AnimatePresence, motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Pause, Play } from 'lucide-react';
import React, { useEffect, useState } from 'react';
// ImageOptimizer removed - using regular img tags

interface Product {
  id: number;
  name: string;
  price: string;
  image: string;
  category: string;
  description?: string;
}

const featuredProducts: Product[] = [
  {
    id: 1,
    name: "Bomba Centrífuga Industrial KSB",
    price: "desde $450.000",
    image: "/bomba-centrifuga-ksb.webp",
    category: "Industrial",
    description: "Alta eficiencia para aplicaciones industriales"
  },
  {
    id: 2,
    name: "Sistema Goteo Automático Premium",
    price: "desde $120.000",
    image: "/sistema-goteo-premium.webp",
    category: "Riego",
    description: "Automatización completa para cultivos"
  },
  {
    id: 3,
    name: "Bomba Sumergible Grundfos",
    price: "desde $280.000",
    image: "/bomba-sumergible-grundfos.webp",
    category: "Doméstico",
    description: "Solución confiable para pozos profundos"
  },
  {
    id: 4,
    name: "Aspersor Rotativo Profesional",
    price: "desde $45.000",
    image: "/aspersor-rotativo.webp",
    category: "Aspersión",
    description: "Cobertura uniforme y eficiente"
  },
  {
    id: 5,
    name: "Controlador Automático IoT",
    price: "desde $180.000",
    image: "/valvula-solenoide-hunter.webp",
    category: "Automatización",
    description: "Control inteligente desde tu smartphone"
  },
  {
    id: 6,
    name: "Válvula Solenoide Hunter",
    price: "desde $35.000",
    image: "/valvula-solenoide-hunter.webp",
    category: "Accesorios",
    description: "Durabilidad y precisión garantizada"
  }
];

export const EnhancedHeroCarousel: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [direction, setDirection] = useState(0);

  useEffect(() => {
    if (isPlaying && !isHovered) {
      const interval = setInterval(() => {
        setDirection(1);
        setCurrentIndex((prev) => (prev + 1) % featuredProducts.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, isHovered]);

  const nextSlide = () => {
    setDirection(1);
    setCurrentIndex((prev) => (prev + 1) % featuredProducts.length);
  };

  const prevSlide = () => {
    setDirection(-1);
    setCurrentIndex((prev) => (prev - 1 + featuredProducts.length) % featuredProducts.length);
  };

  const goToSlide = (index: number) => {
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
      scale: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9
    })
  };

  return (
    <div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-lg border-b border-slate-200/20 dark:border-slate-700/20 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              ⭐ Productos Destacados
            </h2>
            <div className="hidden md:flex items-center space-x-2 text-sm text-slate-500 dark:text-slate-400">
              <span>{currentIndex + 1}</span>
              <span>/</span>
              <span>{featuredProducts.length}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Play/Pause Button */}
            <motion.button
              onClick={() => setIsPlaying(!isPlaying)}
              className="p-2 rounded-full bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isPlaying ? 
                <Pause className="w-4 h-4 text-slate-600 dark:text-slate-300" /> :
                <Play className="w-4 h-4 text-slate-600 dark:text-slate-300" />
              }
            </motion.button>

            {/* Navigation Buttons */}
            <motion.button
              onClick={prevSlide}
              className="p-2 rounded-full bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronLeft className="w-5 h-5 text-slate-600 dark:text-slate-300" />
            </motion.button>
            <motion.button
              onClick={nextSlide}
              className="p-2 rounded-full bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronRight className="w-5 h-5 text-slate-600 dark:text-slate-300" />
            </motion.button>
          </div>
        </div>

        {/* Main Carousel */}
        <div 
          className="relative h-96 rounded-2xl overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <AnimatePresence initial={false} custom={direction}>
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
                scale: { duration: 0.4 }
              }}
              className="absolute inset-0"
            >
              <div className="relative w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 rounded-2xl overflow-hidden">
                {/* Background Image */}
                <img
                  src={featuredProducts[currentIndex].image}
                  alt={featuredProducts[currentIndex].name}
                  className="w-full h-full object-cover"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />

                {/* Content */}
                <div className="absolute inset-0 flex items-center">
                  <div className="max-w-2xl mx-auto px-8 text-white">
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.6 }}
                    >
                      <div className="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                        {featuredProducts[currentIndex].category}
                      </div>
                      <h3 className="text-4xl font-bold mb-4">
                        {featuredProducts[currentIndex].name}
                      </h3>
                      <p className="text-xl text-slate-200 mb-6">
                        {featuredProducts[currentIndex].description}
                      </p>
                      <div className="flex items-center space-x-6">
                        <div className="text-3xl font-bold text-blue-400">
                          {featuredProducts[currentIndex].price}
                        </div>
                        <motion.button
                          className="bg-gradient-to-r from-blue-600 to-cyan-500 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-blue-500/25 transition-all"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Ver Detalles
                        </motion.button>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Progress Bar */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-full h-1 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-400 to-cyan-400"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: 4, ease: "linear" }}
                key={currentIndex}
              />
            </div>
          </div>
        </div>

        {/* Thumbnail Navigation */}
        <div className="flex justify-center mt-6 space-x-3 overflow-x-auto pb-2">
          {featuredProducts.map((product, index) => (
            <motion.button
              key={product.id}
              onClick={() => goToSlide(index)}
              className={`
                flex-shrink-0 w-20 h-20 rounded-xl overflow-hidden border-2 transition-all
                ${index === currentIndex
                  ? 'border-blue-500 scale-110 shadow-lg'
                  : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500'
                }
              `}
              whileHover={{ scale: index === currentIndex ? 1.1 : 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );
};