# 💧 Hidroimplementos SPA

<div align="center">

![Hidroimplementos Logo](public/logo-hidroimplementos.webp)

**Tecnología de Riego Avanzada para Chile** 🇨🇱

[![React](https://img.shields.io/badge/React-18.3.1-61DAFB?style=for-the-badge&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-3178C6?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.8-646CFF?style=for-the-badge&logo=vite)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.13-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)

[🌐 Ver Demo](http://localhost:3000) • [📧 Contacto](mailto:<EMAIL>) • [📱 WhatsApp](https://wa.me/56912345678)

</div>

---

## 🚀 ¿Qué es Hidroimplementos SPA?

**Hidroimplementos SPA** es una empresa líder en **soluciones de riego y bombeo** en La Araucanía y sur de Chile, con **25 años de experiencia** en tecnología de riego avanzada.

### 🎯 Nuestros Servicios

- 🏭 **Bombas Industriales** - KSB, Grundfos y marcas premium
- 🌱 **Sistemas de Riego** - Goteo, aspersión y automatización
- ⚙️ **Automatización** - Válvulas solenoides y control inteligente
- 🔧 **Mantenimiento** - Servicio técnico especializado
- 📦 **Tienda Online** - Catálogo completo con envío a domicilio

---

## 🌟 Características del Sitio Web

### ✨ **Experiencia de Usuario**
- 🎨 **Diseño Moderno** - Interfaz limpia y profesional
- 📱 **Responsive** - Optimizado para móviles, tablets y desktop
- 🌙 **Modo Oscuro** - Cambio automático de tema
- ⚡ **Carga Rápida** - Optimización de imágenes y rendimiento
- 🔍 **Buscador Avanzado** - Encuentra productos específicos fácilmente

### 🛠️ **Funcionalidades Principales**

#### 🔍 **Buscador Avanzado de Bombas**
Réplica exacta del sistema KSB con filtros técnicos:
- Caudal (L/min)
- Altura manométrica (m)
- Tipo de bomba
- Aplicación específica
- Marca y modelo

#### 📞 **Integración WhatsApp**
- Botón flotante personalizado
- Mensajes pre-configurados
- Recolección de nombre del cliente
- Enlace directo a consultas

#### 🏪 **Catálogo de Productos**
- Grid responsive de productos
- Categorización inteligente
- Imágenes optimizadas
- Información técnica detallada

---

## 🛠️ Tecnologías Utilizadas

### **Frontend**
- ⚛️ **React 18** - Biblioteca de UI moderna
- 📘 **TypeScript** - Tipado estático para mayor robustez
- 🎨 **Tailwind CSS** - Framework de estilos utility-first
- 🎭 **Framer Motion** - Animaciones fluidas y profesionales
- 🔥 **Vite** - Build tool ultra-rápido con HMR

### **Optimización**
- 🖼️ **Imágenes WebP** - Formato optimizado para web
- 📦 **Code Splitting** - Carga bajo demanda
- 🚀 **Lazy Loading** - Carga diferida de componentes
- 💾 **Preloading** - Precarga de recursos críticos

### **Herramientas de Desarrollo**
- 🔧 **ESLint** - Linting de código
- 💅 **Prettier** - Formateo automático
- 🐙 **Git** - Control de versiones
- 📝 **Conventional Commits** - Estándar de commits

---

## 🚀 Instalación y Desarrollo

### **Prerrequisitos**
- Node.js 18+ 
- npm o yarn
- Git

### **Instalación**
```bash
# Clonar el repositorio
git clone https://github.com/tu-usuario/hidroimplementos-spa.git

# Navegar al directorio
cd hidroimplementos-spa

# Instalar dependencias
npm install

# Iniciar servidor de desarrollo
npm run dev
```

### **Scripts Disponibles**
```bash
npm run dev          # Servidor de desarrollo
npm run build        # Build de producción
npm run preview      # Preview del build
npm run lint         # Linting del código
npm run type-check   # Verificación de tipos
```

---

## 📁 Estructura del Proyecto

```
src/
├── components/           # Componentes reutilizables
│   ├── sections/        # Secciones específicas
│   ├── AdvancedPumpFinder.tsx
│   ├── Footer.tsx
│   ├── HeroSection.tsx
│   ├── Navbar.tsx
│   ├── ProductGrid.tsx
│   └── ...
├── hooks/               # Custom hooks
│   └── useImagePreloader.ts
├── App.tsx             # Componente principal
└── main.tsx           # Punto de entrada

public/
├── images/             # Imágenes optimizadas
├── logo-hidroimplementos.webp
└── favicon.png
```

---

## 🎨 Características de Diseño

### **Paleta de Colores**
- 🔵 **Azul Principal** - `#2563eb` (Confianza y profesionalismo)
- 🌊 **Cyan Secundario** - `#06b6d4` (Agua y frescura)
- ⚫ **Grises** - Escala completa para textos y fondos
- 🌙 **Modo Oscuro** - Paleta adaptada para baja luminosidad

### **Tipografía**
- **Inter** - Fuente principal (Google Fonts)
- Pesos: 300, 400, 500, 600, 700, 800
- Optimizada para legibilidad en pantallas

---

## 📞 Contacto y Soporte

### **Información de Contacto**
- 🌐 **Sitio Web**: [www.hidroimplementos.cl](http://www.hidroimplementos.cl)
- 📧 **Email**: <EMAIL>
- 📱 **WhatsApp**: +56 9 1234 5678
- 📍 **Dirección**: La Araucanía, Chile

### **Soporte Técnico**
- 🔧 **Mantenimiento**: Servicio técnico especializado
- 📚 **Documentación**: Manuales y guías técnicas
- 🎓 **Capacitación**: Entrenamiento en uso de equipos

---

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

---

<div align="center">

**Desarrollado con ❤️ para Hidroimplementos SPA**

*Tecnología de riego que transforma el futuro agrícola de Chile* 🌱

</div>
